import {
  <PERSON><PERSON>nent,
  OnInit,
  ViewChild,
  After<PERSON>iew<PERSON>nit,
  <PERSON><PERSON><PERSON>roy,
  ChangeDetectorRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatSort, MatSortModule, Sort } from '@angular/material/sort';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatDividerModule } from '@angular/material/divider';
import { MatChipsModule } from '@angular/material/chips';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { HttpClient } from '@angular/common/http';
import { PrDataService } from '../../services/pr-data.service';
import { ApiUrlService } from '../../services/api-url.service';
import { FilterStateService } from '../../services/filter-state.service';
import { ConfigService } from '../../services/config.service';
import { Subject, debounceTime, distinctUntilChanged, takeUntil } from 'rxjs';
import {
  ApiResponse,
  CommentThreadResponse,
  ContributionsHierarchyQueryResponse,
} from '../../models/pr-interfaces.model';
import { ApproverDialogComponent } from '../dialogs/approver-dialog.component';
import { DownloadProgressService } from '../../services/download-progress.service';
import { SignalRService } from '../../services/signalr.service';
import { PrExcelExportService } from '../../services/pr-excel-export.service';
import { DownloadUiComponent } from '../download-ui/download-ui.component';
import { BuildLogsDialogComponent } from '../dialogs/build-logs-dialog.component';
import { TimeService } from '../../services/time.service';
import {
  CdkVirtualScrollViewport,
  ScrollingModule,
} from '@angular/cdk/scrolling';
import { FormsModule } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { PrDataCommonService } from './pr-data-common';
import { PrFilterComponent } from '../pr-filter/pr-filter.component';
import { PrCardHeaderComponent } from '../pr-card-header/pr-card-header.component';

@Component({
  selector: 'app-pr-data',
  standalone: true,
  imports: [
    CommonModule,
    MatTableModule,
    MatSortModule,
    MatInputModule,
    MatFormFieldModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
    MatMenuModule,
    MatDialogModule,
    ScrollingModule,
    FormsModule,
    MatSelectModule,
    PrFilterComponent, // Filter component
    PrCardHeaderComponent, // Card header component
    DownloadUiComponent, // Download UI component
  ],
  templateUrl: './pr-data.component.html',
  styleUrl: './pr-data.component.scss',
})
export class PrDataComponent implements OnInit, AfterViewInit, OnDestroy {
  displayedColumns: string[] = [
    'serialNumber', // Serial number column
    'pullRequestId',
    'title',
    'creationDate',
    'timelapse',
    'activeComments',
    'status',
    'createdBy',
    'reviewers',
    'sourceRefName',
    'pipelineStatus',
  ];
  dataSource = new MatTableDataSource<any>();
  lastUpdated = new Date();
  isLoading = true;
  Math = Math; // Expose Math to the template

  // Filter related properties
  currentFilter = '';
  currentSort: Sort = { active: '', direction: '' };
  private searchTerms = new Subject<string>();
  private destroy$ = new Subject<void>();
  searchMode: 'client' | 'server' = 'client'; // Default to client-side filtering for better UX
  private patToken = '';
  private selectedProject = '';
  private selectedProjectId = '';
  private selectedRepoId = '';
  selectedRepo = ''; // Made public for template access

  // Add this property to store continuation tokens
  private continuationTokens: Map<number, string> = new Map();

  // Cache for pipeline API calls to prevent duplicates
  private _pipelineCallCache: { [key: string]: boolean } = {};

  // Private properties to track pagination state
  private _lastPageRequested = -1;
  private _lastPageSize = 0;
  private _lastApiCallTimestamp = 0;
  // Infinite scroll variables
  prList: any[] = [];
  batchSize: number;
  hasMore = true;
  isLoadingMore = false;
  error: string | null = null;
  private prSet = new Set<number>(); // For deduplication

  // Status filter
  statusFilter: string = 'all';
  statusOptions: Array<{ value: string; label: string }>;

    // Track last status filter to prevent redundant API calls
  private _lastStatusFilter: string = 'all';

  // Date range filter properties
  private startDate: Date | null = null;
  private endDate: Date | null = null;

  totalCount = 0;
  completedCount = 0;
  abandonedCount = 0;

  // Individual loading states for PR counts
  loadingActiveCount = false;
  loadingCompletedCount = false;
  loadingAbandonedCount = false;

  sortSubscription: any;

  // Export functionality
  isExporting = false;
  exportDownloadId: string | null = null;

  @ViewChild(MatSort) sort!: MatSort;
  @ViewChild(CdkVirtualScrollViewport) virtualScroll!: CdkVirtualScrollViewport;
  constructor(
    private http: HttpClient,
    private prDataService: PrDataService,
    private router: Router,
    private apiUrlService: ApiUrlService,
    private cdr: ChangeDetectorRef,
    private dialog: MatDialog,
    public prDataCommon: PrDataCommonService, // Inject shared service
    private configService: ConfigService,
    private timeService: TimeService, // Inject time service
    private signalRService: SignalRService,
    private prExcelExportService: PrExcelExportService,
    private filterStateService: FilterStateService, // Inject filter state service
    private downloadProgressService: DownloadProgressService
  ) {
    this.totalCount = 0;
    this.completedCount = 0;
    this.abandonedCount = 0;
    this.loadingActiveCount = false;
    this.loadingCompletedCount = false;
    this.loadingAbandonedCount = false;
    this.sortSubscription = null;

    // Initialize values from config service
    this.batchSize = this.configService.BATCH_SIZE;
    this.statusOptions = this.configService.PR_STATUS_OPTIONS;
  }
  ngOnInit(): void {
    // Initialize SignalR connection
    this.initializeSignalR();

    // Get saved filter state from service
    const filterState = this.filterStateService.getCurrentFilterState();

    // Initialize component state from filter service
    this.currentFilter = filterState.searchFilter || '';
    this.statusFilter = filterState.statusFilter || 'all';
    this.startDate = filterState.startDate;
    this.endDate = filterState.endDate;

    // Force active filters update to ensure UI state is current
    this.filterStateService.updateFilterState({
      searchFilter: this.currentFilter,
      statusFilter: this.statusFilter,
      startDate: this.startDate,
      endDate: this.endDate,
    });

    // Track the last status filter to prevent duplicate loads
    this._lastStatusFilter = this.statusFilter;

    // Subscribe to filter state changes to stay in sync
    this.filterStateService.filterState$
      .pipe(takeUntil(this.destroy$))
      .subscribe((state) => {
        // Only update if values are different to prevent loops
        let needsReload = false;

        // Update status filter if changed, but don't reload if it was just changed via setStatusFilterAndLoad
        if (this.statusFilter !== state.statusFilter) {
          // Check if this is a new status filter change (not triggered by setStatusFilterAndLoad)
          // or if this filter change was already handled elsewhere
          if (state.statusFilter !== this._lastStatusFilter) {
            this.statusFilter = state.statusFilter;
            this._lastStatusFilter = state.statusFilter;
            needsReload = true;
          } else {
            this.statusFilter = state.statusFilter;
          }
        }

        // Update search filter if changed
        if (this.currentFilter !== state.searchFilter) {
          this.currentFilter = state.searchFilter;
          needsReload = true;
        }

        // Update date filters if changed
        const startDateChanged =
          (!this.startDate && state.startDate) ||
          (this.startDate && !state.startDate) ||
          (this.startDate &&
            state.startDate &&
            this.startDate.getTime() !== state.startDate.getTime());

        const endDateChanged =
          (!this.endDate && state.endDate) ||
          (this.endDate && !state.endDate) ||
          (this.endDate &&
            state.endDate &&
            this.endDate.getTime() !== state.endDate.getTime());

        if (startDateChanged) {
          this.startDate = state.startDate;
          needsReload = true;
        }

        if (endDateChanged) {
          this.endDate = state.endDate;
          needsReload = true;
        }

        // Reload data if needed and not already loading
        if (needsReload && !this.isLoading) {
          // Check if filters are being cleared
          const filtersCleared =
            state.searchFilter === '' &&
            (this.currentFilter !== '' ||
              (state.statusFilter === 'all' && this.statusFilter !== 'all'));

          if (filtersCleared) {
            // Reset data to prevent duplicates
            this.prList = [];
            this.prSet.clear();
            this.dataSource.data = [];

            // Reset pipeline loading flags
            if (this.dataSource.data) {
              for (const pr of this.dataSource.data) {
                pr.pipelineStatusLoaded = false;
                pr.loadingPipelineStatus = false;

                // Reset status to ensure it shows loading state
                if (pr.pipelineStatus === 'Not loaded') {
                  pr.pipelineStatus = undefined;
                }
              }
            }

            // Clear the pipeline cache
            this._pipelineCallCache = {};

            // Load data with pipeline status
            this.loadPrData(false);
          } else {
            // For other filter changes, reset data first to prevent duplicates
            this.prList = [];
            this.prSet.clear();
            this.dataSource.data = [];

            // Use normal loading
            this.loadPrData();
          }
        }
      });

    // Set up the debounced search term processing
    this.searchTerms
      .pipe(
        takeUntil(this.destroy$),
        debounceTime(this.configService.SEARCH_DEBOUNCE_TIME), // Use configurable debounce time
        distinctUntilChanged() // Only emit if value is different from previous
      )
      .subscribe((term: string) => {
        this.isLoading = true; // Show loading indicator
        this.currentFilter = term;

        // Store the search term in filter state service
        this.filterStateService.updateFilterState({ searchFilter: term });

        // For empty search, reset everything and reload data
        if (!term) {
          this.currentFilter = '';
          this.dataSource.filter = '';
          this.searchMode = 'server';

          // Check if this is from direct user input or initial load
          // For empty terms from clearSearch() we already handle pipeline status there
          // For other empty terms (like initial load), load pipeline status normally
          this.loadPrData(false);
          return;
        }

        // Clear continuation tokens when filter changes
        this.continuationTokens.clear();

        // Try to find PR by ID in current data (client-side)
        const isNumeric = /^\d+$/.test(term);
        let found = false;
        if (isNumeric) {
          // Search by PR ID
          found = this.dataSource.data.some(
            (pr) => pr.pullRequestId?.toString() === term
          );
          if (found) {
            // Use client-side filter for exact PR ID match
            this.searchMode = 'client';
            this.applyClientSideFilter(term);
            return;
          } else {
            // If not found, call the API to get PR by ID directly
            this.isLoading = true;
            this.error = null;
            const url = this.apiUrlService.getPullRequestByIdUrl(
              term,
              this.selectedProject,
              this.selectedRepo,
              this.patToken
            );
            this.http.get<any>(url).subscribe({
              next: (response) => {
                if (response && response.data) {
                  this.dataSource.data = [response.data];
                  this.prList = [response.data];
                  this.error = null;
                  this.searchMode = 'client';

                  // Fetch comments for this PR - but without pipeline status since we'll do that separately
                  // This separates the comment fetching from pipeline status fetching
                  this.fetchCommentsOnlyForPRs([response.data]);

                  // Now explicitly fetch the pipeline status for this specific PR
                  if (
                    this.selectedProjectId &&
                    this.selectedRepoId &&
                    response.data.pullRequestId
                  ) {
                    this.fetchPipelineStatus(response.data);
                  }
                } else {
                  this.dataSource.data = [];
                  this.prList = [];
                  this.error = `Pull request with ID "${term}" not found`;
                }
                this.isLoading = false;
              },
              error: (err) => {
                console.error('API error when searching for PR:', err);
                this.dataSource.data = [];
                this.prList = [];
                this.isLoading = false;
                this.error = `Error searching for PR: ${
                  err.message || 'Unknown error'
                }`;

                // If there's a network or server error, let the user know specifically
                if (err.status === 0) {
                  this.error = 'Network error. Please check your connection.';
                } else if (err.status >= 500) {
                  this.error = `Server error (${err.status}). Please try again later.`;
                }
              },
            });
            return;
          }
        } else {
          // Search by other fields (title, author, etc.)
          found = this.dataSource.data.some((pr) => {
            return (
              (pr.title && pr.title.toLowerCase().includes(term)) ||
              (pr.createdBy?.displayName &&
                pr.createdBy.displayName.toLowerCase().includes(term)) ||
              (pr.status && pr.status.toLowerCase().includes(term))
            );
          });
          if (found) {
            this.searchMode = 'client';
            this.applyClientSideFilter(term);
            return;
          }
        }

        // Determine search mode based on filter length and data size
        if (term.length <= 2 || this.dataSource.data.length > 200) {
          // Short queries or large datasets should use server-side filtering
          this.searchMode = 'server';
          this.loadPrData(true); // Skip pipeline status during search
        } else {
          // For longer queries on smaller datasets, client-side is more responsive
          this.searchMode = 'client';
          this.applyClientSideFilter(term);
        }
      });

    // Subscribe to credentials
    this.prDataService.credentials$.subscribe((creds) => {
      if (creds) {
        // Clear pipeline cache when credentials change
        this._pipelineCallCache = {};

        // Store updated credentials
        this.patToken = creds.patToken;
        this.selectedProject = creds.project;
        this.selectedRepo = creds.repository;
        this.selectedProjectId = creds.projectId || '';
        this.selectedRepoId = creds.repositoryId || '';

        // Restore filter state if available
        const filterState = this.filterStateService.getCurrentFilterState();

        // Only restore if we have a saved state that's not default
        const hasFilterState =
          filterState.searchFilter !== '' ||
          filterState.statusFilter !== 'all' ||
          filterState.startDate !== null ||
          filterState.endDate !== null;

        if (hasFilterState) {
          // Restore status filter
          this.statusFilter = filterState.statusFilter;

          // Restore date filters
          this.startDate = filterState.startDate;
          this.endDate = filterState.endDate;

          // Restore search filter
          if (filterState.searchFilter) {
            this.currentFilter = filterState.searchFilter;
            // Emit through searchTerms to trigger the search flow
            setTimeout(() => this.searchTerms.next(this.currentFilter), 0);
          } else {
            // Initial data load with server-side mode and restored filters
            this.searchMode = 'server';
            this._lastApiCallTimestamp = 0; // Reset timestamp to force load
            this.loadPrData();
          }
        } else {
          // Initial data load with server-side mode
          this.searchMode = 'server';
          this._lastApiCallTimestamp = 0; // Reset timestamp to force load
          this.loadPrData();
        }
      } else {
        ('No credentials found, checking localStorage...');
        // Try to reload credentials from localStorage
        this.prDataService.loadCredentialsFromStorage();
      }
    });

    // Use cached PR counts when loading initial data (don't force refresh)
    this.fetchPrStatusCounts(false);
    // Initial load
    this.loadInitialPRs();
  }
  ngAfterViewInit() {
    // Connect the sort to the data source
    this.dataSource.sort = this.sort;

    // Set up custom filter predicate using the shared service
    this.prDataCommon.setupFilterPredicate(
      this.dataSource,
      this.formatBranchName.bind(this)
    );

    // Set up sort change listener
    if (this.sortSubscription) {
      this.sortSubscription.unsubscribe();
    }

    this.sortSubscription = this.sort.sortChange.subscribe((sort: Sort) => {
      this.currentSort = sort;

      // Only client-side sorting for timelapse column
      if (sort.active === 'timelapse') {
        this.sortTimelapseColumn(sort.direction);
      } else {
        // Let MatTableDataSource handle other columns
        this.dataSource.sort = this.sort;
      }
    });

    // Custom sorting logic for all columns through the data accessor
    this.dataSource.sortingDataAccessor = (item, property) => {
      switch (property) {
        case 'timelapse':
          return this.getTimelapseHours(item);
        default:
          return item[property];
      }
    };

    // Force the dataSource to be sorted using our custom sortingDataAccessor
    setTimeout(() => {
      // This ensures that the sort mechanism is initialized properly
      this.dataSource._updateChangeSubscription();

      // If there's an existing sort, apply it
      if (this.currentSort && this.currentSort.active) {
        this.sort.active = this.currentSort.active;
        this.sort.direction = this.currentSort.direction;

        if (this.currentSort.active === 'timelapse') {
          this.sortTimelapseColumn(this.currentSort.direction);
        } else {
          this.dataSource.sort = this.sort;
        }
      }
    }, 100);
  }
  sortTimelapseColumn(direction: 'asc' | 'desc' | ''): void {
    if (!direction) return;

    // Make a copy of the data before sorting
    const dataToSort = [...this.prList];

    // Sort the array by calculated timelapse
    dataToSort.sort((a, b) => {
      const aTime = this.getTimelapseHours(a);
      const bTime = this.getTimelapseHours(b);
      if (aTime === bTime) return 0;
      return direction === 'asc' ? aTime - bTime : bTime - aTime;
    });

    // Update both the arrays to ensure consistency
    this.prList = dataToSort;
    this.dataSource.data = dataToSort;

    // Force update in case the table is showing filtered data
    if (this.searchMode === 'client') {
      this.applyClientSideFilter(this.currentFilter);
    }
  }
  getTimelapseHours(pr: any): number {
    // Delegate to the time service
    return this.timeService.getTimelapseHours(pr);
  }
  loadPrData(skipPipelineStatus = false) {
    // Prevent multiple calls within a short time period
    const now = Date.now();
    if (
      now - this._lastApiCallTimestamp <
      this.configService.API_DEBOUNCE_TIME
    ) {
      return;
    }

    // Update the timestamp
    this._lastApiCallTimestamp = now;

    // Always start with loading state
    this.isLoading = true;

    // Always reset data before loading to prevent duplicates - but save the status filter
    this.prSet.clear();

    // Store the current status filter to avoid duplicate loads
    this._lastStatusFilter = this.statusFilter;

    // If we're loading with pipeline status, reset any pipeline flags in the existing data
    // to ensure fresh pipeline status data is loaded
    if (
      !skipPipelineStatus &&
      this.dataSource.data &&
      this.dataSource.data.length > 0
    ) {
      console.log(
        'Resetting pipeline status flags for existing PRs to ensure fresh data'
      );
      for (const pr of this.dataSource.data) {
        pr.pipelineStatusLoaded = false;
        pr.loadingPipelineStatus = false;
      }
    }

    // Always use server mode for empty filter
    if (!this.currentFilter) {
      this.searchMode = 'server';
    } // Calculate pagination parameters
    const skip = 0; // Always start from 0 for infinite scroll
    const top = this.batchSize;

    console.log(
      `Loading data: page=${0}, size=${
        this.batchSize
      }, skip=${skip}, top=${top}, status=${this.statusFilter}`
    );

    // Use common service to prepare filter parameters
    const filterParam = this.prDataCommon.formatFilterParams(
      this.currentFilter,
      this.statusFilter,
      this.searchMode
    );

    // Remove server-side sorting since API doesn't support it properly
    // All sorting will be handled client-side

    // Use common service to format date parameters
    const { minTime, maxTime } = this.prDataCommon.formatDateParams(
      this.startDate,
      this.endDate
    );

    // Get URL from service
    const url = this.apiUrlService.getPullRequestsUrl(
      top,
      skip,
      this.selectedProject,
      this.selectedRepo,
      this.patToken,
      filterParam,
      undefined, // Removed orderBy parameter to disable server-side sorting
      this.statusFilter, // Pass status explicitly
      minTime, // Start date for filtering
      maxTime // End date for filtering
    );

    // Make the HTTP request - simplified for reliability
    this.http.get<ApiResponse>(url).subscribe({
      next: (response) => {
        if (response && response.data) {
          console.log(response,'response1111')
          // Clear any existing filter
          this.dataSource.filter = '';

          // Update data source with new data
          this.dataSource.data = response.data;

          // Apply current sort to the data if we have an active sort
          if (this.currentSort && this.currentSort.direction) {
            if (this.currentSort.active === 'timelapse') {
              this.sortTimelapseColumn(this.currentSort.direction);
            } else {
              // For other columns, let dataSource handle it
              this.dataSource.sort = this.sort;
            }
          } // Fetch active comments for each PR
          this.fetchActiveCommentsForPRs(response.data, skipPipelineStatus);
          // Update prList with the new data for infinite scrolling
          this.prList = [...response.data];

          // If date filters are applied, count PRs from loaded data
          // Otherwise use the counts from API response if available
          // if (this.hasDateFilters()) {
          //   console.log('Date filter applied - updating PR counts from loaded data');
          //   this.countPrsByStatus(this.prList);
          // } else
          if (response.totalCount !== undefined) {
            this.totalCount = response.totalCount;
          }

          this.lastUpdated = new Date();

          // If we're loading with pipeline status, check for stuck status
          if (!skipPipelineStatus) {
            // Wait a moment to make sure all other operations complete
            setTimeout(() => {
              this.forceReloadStuckPipelineStatus();
            }, 100);
          }
        } else {
          console.warn('No PR data found or invalid format');
          this.dataSource.data = [];
        }

        // Hide loading indicator
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error fetching PR data:', error);
        this.isLoading = false;
        this.dataSource.data = [];
      },
    });
  }
  applyFilter(event: any) {
    let filterValue: string;

    // Handle null/undefined event
    if (!event) {
      console.warn('Empty event received in applyFilter');
      filterValue = '';
    }
    // Handle both Event objects and direct string values (from ngModelChange)
    else if (typeof event === 'string') {
      filterValue = event.trim();
    } else if (event instanceof Event && 'target' in event && event.target) {
      filterValue = ((event.target as HTMLInputElement)?.value || '').trim();
    } else if (typeof event === 'object' && event && 'detail' in event) {
      // Handle CustomEvent with detail property
      filterValue = (event.detail || '').toString().trim();
    } else {
      console.warn('Unrecognized event type in applyFilter:', event);
      filterValue = '';
    }

    // Store current filter
    this.currentFilter = filterValue;

    // Validate if it's a numeric PR ID
    if (filterValue && !/^\d+$/.test(filterValue)) {
      this.error = 'Please enter a valid PR ID number';
      this.isLoading = false;
      return;
    } else {
      this.error = null;
    }

    // Always use the searchTerms subject to benefit from debounce
    // This will route the search directly to the specific GetGitPullRequestById API
    this.searchTerms.next(filterValue);

    // Show immediate feedback that the search is in progress
    if (filterValue) {
      this.isLoading = true;
    }

    // Make sure the filter state service is updated
    this.filterStateService.updateFilterState({ searchFilter: filterValue });
  }

  // Clear search field and reset filter
  clearSearch(input?: HTMLInputElement): void {
    // Clear input field if provided
    if (input) {
      input.value = '';
    }

    // Clear the current filter value
    this.currentFilter = '';

    // Clear any errors
    this.error = null;

    // Update filter state service
    this.filterStateService.updateFilterState({ searchFilter: '' });

    // Since this is explicitly clearing the search, bypass the normal search pipeline
    // and directly load data with pipeline status included
    this.isLoading = true;

    // Reset any pipeline-related flags to ensure we get fresh data
    if (this.dataSource.data) {
      for (const pr of this.dataSource.data) {
        pr.pipelineStatusLoaded = false;
        pr.loadingPipelineStatus = false;
      }
    }

    // Clear pipeline call cache
    this._pipelineCallCache = {};

    // Load data with pipeline status (false = don't skip pipeline status)
    this.loadPrData(false);
  }
  /**
   * Handle date range changes from the PR filter component
   * @param dateRange The selected date range with startDate and endDate
   */
  onDateRangeChanged(dateRange: {
    startDate: Date | null;
    endDate: Date | null;
  }): void {
    // Store previous filter state to detect changes in filter type
    const hadDateFiltersBefore = this.hasDateFilters();

    // Update filter values
    this.startDate = dateRange.startDate;
    this.endDate = dateRange.endDate;

    // Save filter state to service
    this.filterStateService.updateFilterState({
      startDate: dateRange.startDate,
      endDate: dateRange.endDate,
    });

    // Detect if we switched between filtered/unfiltered state
    const hasDateFiltersNow = this.hasDateFilters();
    const filterTypeChanged = hadDateFiltersBefore !== hasDateFiltersNow;

    // Set loading states for PR counts
    // this.loadingActiveCount = true;
    // this.loadingCompletedCount = true;
    // this.loadingAbandonedCount = true;

    // Reset data for new filter
    this.prList = [];
    this.prSet.clear();
    this.hasMore = true;
    this.continuationTokens.clear(); // Clear continuation tokens for clean reload

    // Reload data with the new date range
    this.isLoading = true;
    this._lastApiCallTimestamp = 0; // Force a reload

    // If filters were cleared (switching from filtered to unfiltered state)
    // Use the count API for better performance
    if (filterTypeChanged && !this.hasDateFilters()) {
      this.fetchPrStatusCounts(true);
    }

    // Always load the data whether filtered or not
    this.loadPrData();
  }

  // Client-side filtering
  applyClientSideFilter(filterValue: string) {
    // Apply filter to the MatTableDataSource
    this.dataSource.filter = filterValue;

    // Log the filtered data count
    `Filter results: ${this.dataSource.filteredData.length} of ${this.dataSource.data.length} records match`;

    // If the filter returns very few results or many results, switch to server-side filtering
    if (
      (this.dataSource.filteredData.length < 5 ||
        this.dataSource.filteredData.length > 90) &&
      this.dataSource.data.length > 100
    ) {
      ('Switching to server-side filtering for more accurate results');
      this.searchMode = 'server';
      this.loadPrData(true); // Skip pipeline status during search-related operations
    }
  }
  // Custom filter predicate for MatTableDataSource  // setupFilterPredicate method removed - now using the shared service implementation

  getStatusClass(status: string): string {
    status = status.toLowerCase();
    if (status === 'active') return 'status-active';
    if (status === 'completed') return 'status-completed';
    if (status === 'abandoned') return 'status-abandoned';
    if (status === 'draft') return 'status-draft';
    return '';
  }

  goBack() {
    this.router.navigate(['/']);
  }

  /**
   * Gets the repository name without the refs/heads/ prefix
   */ formatBranchName(branchName: string): string {
    return branchName.replace('refs/heads/', '');
  }
  // Navigate to the PR details page
  navigateToPrDetails(prId: number): void {
    // Filter state is already saved through the filter handlers
    // We can navigate directly - when we return, our ngOnInit will restore state
    this.router.navigate(['/pr-details', prId]);
  }

  /**
   * Initialize SignalR connection
   */
  private async initializeSignalR(): Promise<void> {
    try {
      await this.signalRService.startConnection();
      console.log('SignalR connection established for PR data component');
    } catch (error) {
      console.error('Failed to establish SignalR connection:', error);
    }
  }

  // Export PR table data to Excel using SignalR
  async exportToExcel(): Promise<void> {
    if (this.isExporting || this.dataSource.data.length === 0) {
      return;
    }

    try {
      this.isExporting = true;

      // Ensure SignalR connection is established
      if (!this.signalRService.isConnected()) {
        await this.initializeSignalR();
      }

      // Prepare export request
      const exportRequest = {
        project: this.selectedProject,
        repositories: this.selectedRepo,
        patToken: this.patToken,
        status: this.statusFilter !== 'all' ? this.statusFilter : undefined,
        searchTerm: this.currentFilter || undefined,
        startDate: this.startDate ? this.startDate.toISOString().split('T')[0] : undefined,
        endDate: this.endDate ? this.endDate.toISOString().split('T')[0] : undefined,
        pageNumber: 0,
        pageSize: 10000 // Large page size to get all data
      };

      // Start the export job
      this.prExcelExportService.startExcelExport(exportRequest).subscribe({
        next: (response) => {
          if (response.succcess && response.data) {
            const jobUuid = response.data;
            console.log('Export job started with UUID:', jobUuid);

            // Start SignalR-tracked download progress
            const timestamp = new Date().toISOString().split('T')[0];
            const fileName = `PR_Export_${this.selectedRepo}_${timestamp}.xlsx`;
            const { id, progress$ } = this.downloadProgressService.startSignalRDownload(fileName, jobUuid);
            this.exportDownloadId = id;

            // Subscribe to progress updates to handle completion
            progress$.subscribe(progress => {
              if (progress.status === 'completed' && progress.uuid) {
                // Trigger file download using the backend download endpoint
                this.downloadExcelFile(progress.uuid, progress.fileName);
              }
            });

          } else {
            throw new Error(response.message || 'Failed to start export job');
          }
        },
        error: (error) => {
          console.error('Error starting export job:', error);
          this.isExporting = false;
          // Show error message to user
          alert('Failed to start Excel export. Please try again.');
        }
      });

    } catch (error) {
      console.error('Error exporting to Excel:', error);
      this.isExporting = false;
      if (this.exportDownloadId) {
        this.downloadProgressService.setDownloadError(
          this.exportDownloadId,
          'Failed to export PR table data to Excel'
        );
      }
    } finally {
      // Reset export state after a delay (only if no download is in progress)
      setTimeout(() => {
        if (!this.exportDownloadId) {
          this.isExporting = false;
        }
      }, 1000);
    }
  }

  /**
   * Download the exported Excel file using the backend download endpoint
   */
  private downloadExcelFile(uuid: string, fileName: string): void {
    const downloadUrl = this.prExcelExportService.downloadExportedFile(uuid);

    // Create a temporary link to trigger download
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = fileName;
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    console.log('Excel file download initiated:', fileName);

    // Reset export state after download
    setTimeout(() => {
      this.isExporting = false;
      this.exportDownloadId = null;
    }, 2000);
  }

  // Refresh the data
  refreshData(): void {
    // Reset pagination state
    this._lastApiCallTimestamp = 0;

    // Clear pipeline call cache for fresh data
    this._pipelineCallCache = {};

    // Reset pipeline status loaded flags to force reload
    if (this.dataSource.data) {
      for (const pr of this.dataSource.data) {
        pr.pipelineStatusLoaded = false;
        pr.loadingPipelineStatus = false;

        // Reset status to trigger reload
        if (
          pr.pipelineStatus === 'Loading...' ||
          pr.pipelineStatus === 'Not loaded'
        ) {
          pr.pipelineStatus = undefined;
        }
      }
    }

    // Refresh PR status counts with force refresh to bypass cache
    this.fetchPrStatusCounts(true);

    // Load data with current pagination settings - always include pipeline status on manual refresh
    this.loadPrData(false);
  }
  /**
   * Sets default active comments count for pull requests
   * @param prData Array of pull request data
   * @param skipPipelineStatus Optional flag to skip pipeline status fetching (default: false)
   */
  fetchActiveCommentsForPRs(prData: any[], skipPipelineStatus = false) {
    if (!prData || prData.length === 0) return;

    // Set a default value for active comments and initialize pipeline status
    for (const pr of prData) {
      // Initialize active comments if not already set
      if (pr.activeComments === undefined) {
        pr.activeComments = 0;
      }

      // Initialize pipeline status if not already set
      if (pr.pipelineStatus === undefined) {
        if (skipPipelineStatus) {
          // If we're skipping pipeline status, set a neutral display value instead of "Loading..."
          pr.pipelineStatus = 'Not loaded';
          pr.pipelineStatusDisplay = 0;
        } else {
          // Only show "Loading..." if we're actually going to load the status
          pr.pipelineStatus = 'Loading...';
          pr.pipelineStatusDisplay = 0;
        }
      }

      // Fetch pipeline status for each PR if not already loaded and not skipping
      if (
        !skipPipelineStatus &&
        this.selectedProjectId &&
        this.selectedRepoId &&
        !pr.pipelineStatusLoaded &&
        !pr.loadingPipelineStatus
      ) {
        this.fetchPipelineStatus(pr);
      }
    }

    // Update the data source
    this.dataSource.data = [...prData];
  }
  /**
   * Fetches the actual comment count for a specific PR
   * @param pr The pull request to fetch comments for
   * @param event The click event
   */
  fetchCommentCount(pr: any, event: Event): void {
    // Prevent navigation to PR details
    event.stopPropagation();

    // If already loading, don't make another request
    if (pr.loadingComments) {
      return;
    }

    // Show loading state for this specific row
    pr.loadingComments = true; // API endpoint to get comment threads, using ConfigService endpoint
    const url = `${this.configService.getCommentThreadsEndpoint()}?pullRequestId=${
      pr.pullRequestId
    }&project=${this.selectedProject}&repositories=${
      this.selectedRepo
    }&patToken=${this.patToken}`;

    this.http.get<CommentThreadResponse>(url).subscribe({
      next: (response) => {
        if (
          response &&
          response.data &&
          response.data.commentThread &&
          response.data.commentThread.commentThreads
        ) {
          // Count active and fixed threads based on the new response structure
          const threads = response.data.commentThread.commentThreads;
          const activeThreads = threads.filter(
            (thread: any) => thread.status === 'active'
          );
          const fixedThreads = threads.filter(
            (thread: any) => thread.status === 'fixed'
          );

          // Update the active comments count
          pr.activeComments = activeThreads.length;
          pr.resolvedComments = fixedThreads.length;
          pr.totalComments = threads.length;
        } else {
          console.warn('No comment threads found for PR #' + pr.pullRequestId);
          // Reset counts if no data found
          pr.activeComments = 0;
          pr.resolvedComments = 0;
          pr.totalComments = 0;
        }
        // Remove loading state
        pr.loadingComments = false;
      },
      error: (err) => {
        console.error('Error fetching comment threads:', err);
        // Remove loading state on error
        pr.loadingComments = false;
        // Set a default error state
        pr.activeComments = 0;
      },
    });
  }
  /**
   * Fetches pipeline status for a specific PR
   * @param pr The pull request to fetch pipeline status for
   */
  fetchPipelineStatus(pr: any): void {
    // Check if we have the necessary IDs
    if (!this.selectedProjectId || !this.selectedRepoId) {
      console.warn(
        'Repository ID or Project ID is missing, cannot fetch pipeline status'
      );
      return;
    }

    // Create a cache key for this PR/project/repo combination
    const cacheKey = `pipeline-${pr.pullRequestId}-${this.selectedProjectId}-${this.selectedRepoId}`;

    // Check if we're already loading for this PR
    if (pr.loadingPipelineStatus) {
      return;
    }

    // Check if we already have valid pipeline data for this PR that was successfully loaded
    if (
      pr.pipelineStatusLoaded &&
      pr.pipelineStatus !== 'Loading...' &&
      pr.pipelineStatus !== 'Not loaded'
    ) {
      return;
    }

    // Check if there's an in-flight request for this PR in the cache
    if (this._pipelineCallCache && this._pipelineCallCache[cacheKey]) {
      return;
    }

    // Mark as being loaded both on the PR and in the cache
    pr.loadingPipelineStatus = true;
    if (!this._pipelineCallCache) this._pipelineCallCache = {};
    this._pipelineCallCache[cacheKey] = true;
   
   const abc = {
  prIds:[28041,28040,28039,28038,28037,28036,28035,28034,28033,28032,28031,28030,28029,28028,28027,28026,28025,28024,28023,28022,28021],
  repositoryId:"539f64b4-69e9-4280-91f4-60aa0096aead",
  projectId:"4220ea73-9afe-4b22-9145-a7603305bbbb",
  patToken:"4fLZ5T0gLSp7bY5mqWqItN7TM7QijA9utLOtnKlTuUwUnRGXjr5tJQQJ99BEACAAAAAeHFzxAAASAZDOuLoH"
}
  const jsonData = JSON.stringify(abc);
 
    // Base64 encode the JSON data using btoa()
    const base64Data = btoa(encodeURIComponent(jsonData)); console.log(base64Data);
    const url = this.apiUrlService.getContributionsHierarchyQueryUrl(
      pr.pullRequestId,
      this.selectedRepoId,
      this.selectedProjectId,
      this.patToken
    );
    this.http.get<ContributionsHierarchyQueryResponse>(url).subscribe({
      next: (response) => {
        if (
          response &&
          response.data &&
          response.data.dataProviders?.prDetailDataProvider?.policies
        ) {
          this.prDataCommon.processPolicies(
            pr,
            response.data.dataProviders.prDetailDataProvider.policies
          );
        } else {
          pr.pipelinePolicies = [];
          pr.activePolicy = null;
          pr.pipelineStatus = 'No data';
          pr.pipelineStatusDisplay = 0;
        }
        // Mark as loaded to prevent duplicate calls
        pr.pipelineStatusLoaded = true;
        pr.loadingPipelineStatus = false;

        // Remove from cache as the request is completed
        if (this._pipelineCallCache && this._pipelineCallCache[cacheKey]) {
          delete this._pipelineCallCache[cacheKey];
        }
      },
      error: (err) => {
        pr.pipelineStatus = 'Error';
        pr.pipelineStatusDisplay = 0;
        // Mark as loaded to prevent continuous retry on error
        pr.pipelineStatusLoaded = true;
        pr.loadingPipelineStatus = false;

        // Remove from cache on error as well
        if (this._pipelineCallCache && this._pipelineCallCache[cacheKey]) {
          delete this._pipelineCallCache[cacheKey];
        }
      },
    });
  }

  /**
   * Processes pipeline policies: sorts by running first, then by evaluation date, and sets the most relevant policy.
   */ /**
   * Processes pipeline policies: sorts by running first, then by evaluation date, and sets the most relevant policy.
   */
  processPolicies(pr: any, policies: any[]) {
    // Method implementation moved to PrDataCommonService
    return this.prDataCommon.processPolicies(pr, policies);
  }
  // --- Status count methods - using the new API ---
  // refreshAllPrCounts(): void {
  //   this.fetchPrStatusCounts();
  // }

  /**
   * Sets the status filter based on the clicked PR count tab and reloads the data
   * @param status The status filter value to apply ('active', 'completed', 'abandoned', or 'all')
   */
  setStatusFilterAndLoad(status: string): void {
    // Only update and reload if the filter has actually changed
    if (this.statusFilter !== status) {
      // Store the previous status to check for significant changes
      const previousStatus = this.statusFilter;

      // Update status filter value
      this.statusFilter = status;

      // Set a flag in the filter state update to indicate this update is from a direct selection
      // This will prevent the filterState$ subscription from triggering another data load
      this.filterStateService.updateFilterState(
        {
          statusFilter: status,
        },
        true
      );

      // Reset the data to prevent duplicates
      this.prList = [];
      this.prSet.clear();
      this.dataSource.data = [];
      this.hasMore = true;
      this.isLoading = true;

      // Clear the pipeline call cache to ensure fresh pipeline status
      this._pipelineCallCache = {};

      // Reset all data and pagination
      this._lastApiCallTimestamp = 0;
      this.loadInitialPRs();
    } else {
    }
  }

  // Legacy methods for backward compatibility
  getActivePRCount(): number {
    // This is now replaced by the counts stored in this.totalCount
    return this.totalCount;
  }

  getCompletedPRCount(): number {
    // This is now replaced by the counts stored in this.completedCount
    return this.completedCount;
  }
  getPipelineStatusIcon(status: number): string {
    return this.prDataCommon.getPipelineStatusIcon(status);
  }
  getPipelineStatusClass(status: number): string {
    return this.prDataCommon.getPipelineStatusClass(status);
  }
  /**
   * Opens the build timeline dialog to show build logs
   * @param pr The pull request with build information
   */ openBuildTimeline(pr: any): void {
    // Open the dialog with pipeline policies if available, else fallback to buildId
    this.dialog.open(BuildLogsDialogComponent, {
      width: '1000px',
      height: '90vh',
      maxWidth: '95vw',
      maxHeight: '95vh',
      data: {
        patToken: this.patToken,
        projectId: this.selectedProjectId,
        buildId: pr.buildId,
        pipelinePolicies: pr.pipelinePolicies || [],
      },
    });
  }
  /**
   * Returns a human-readable timelapse string for a PR
   * @param pr The pull request object
   */ getTimelapse(pr: any): any {
    // Delegate to the time service
    return this.timeService.getTimelapse(pr);
  }

  /**
   * Returns the most relevant pipeline policy for display:
   * - If any policy is in progress, return that.
   * - Else if any policy failed, return that.
   * - Else if all succeeded, return the last one.
   * - Else undefined.
   */
  getPrimaryPipelinePolicy(pr: any): any {
    return this.prDataCommon.getPrimaryPipelinePolicy(pr);
  }
  ngOnDestroy() {
    // Clean up subscriptions
    this.destroy$.next();
    this.destroy$.complete();

    // Clean up direct subscriptions
    if (this.sortSubscription) {
      this.sortSubscription.unsubscribe();
    }

    // Clean up SignalR connection
    this.signalRService.stopConnection().catch(error => {
      console.error('Error stopping SignalR connection:', error);
    });

    // We don't need to unsubscribe from prDataService observables
    // since they're shared services that will persist for the app lifecycle
  }
  openApproverModal(reviewers: any[]) {
    this.dialog.open(ApproverDialogComponent, {
      width: '500px',
      data: { reviewers: reviewers || [] },
    });
  }
  loadInitialPRs() {
    // Reset all data
    this.prList = [];
    this.prSet.clear();
    this.hasMore = true;
    this.error = null;
    this.isLoadingMore = false;

    // Always update the data source immediately to clear any previous data
    // This prevents duplicate PRs from showing in the UI while new data loads
    this.dataSource.data = [];

    // If no date filters, fetch PR counts from API
    if (!this.hasDateFilters()) {
      this.fetchPrStatusCounts(false);
    }

    // Reset sorting if we're loading from scratch
    if (this.sort) {
      // Keep track of the current sort to reapply after loading
      const currentSort = this.currentSort;

      // Clear the sort in the MatSort directive
      this.sort.active = '';
      this.sort.direction = '';
      this.currentSort = { active: '', direction: '' };

      // Store the current status filter to avoid duplicate loads
      this._lastStatusFilter = this.statusFilter;

      // Load data, then reapply sort if needed
      // Skip pipeline status if we're in search mode
      const isSearching = !!this.currentFilter;
      this.loadMorePRs(isSearching);

      if (currentSort && currentSort.direction) {
        setTimeout(() => {
          this.sort.active = currentSort.active;
          this.sort.direction = currentSort.direction;
          this.currentSort = currentSort;

          if (currentSort.active === 'timelapse') {
            this.sortTimelapseColumn(currentSort.direction);
          }
        }, 500); // Give it some time to load the data first
      }
    } else {
      // Store the current status filter to avoid duplicate loads
      this._lastStatusFilter = this.statusFilter;

      this.loadMorePRs();
    }
  }
  loadMorePRs(skipPipelineStatus = false) {
    if (this.isLoadingMore || !this.hasMore) return;
    this.isLoadingMore = true;
    const top = this.batchSize;
    const skip = this.prList.length;

    // Use common service to prepare filter parameters
    const filterParam = this.prDataCommon.formatFilterParams(
      this.currentFilter,
      this.statusFilter,
      'server' // Always use server mode for infinite scroll loading
    );

    // Use common service to format date parameters
    const { minTime, maxTime } = this.prDataCommon.formatDateParams(
      this.startDate,
      this.endDate
    );

    const url = this.apiUrlService.getPullRequestsUrl(
      top,
      skip,
      this.selectedProject,
      this.selectedRepo,
      this.patToken,
      filterParam || undefined,
      undefined, // Remove server-side sorting
      this.statusFilter, // Pass status explicitly
      minTime, // Start date for filtering
      maxTime // End date for filtering
    );
    this.http.get<any>(url).subscribe({
      next: (res) => {
        // First ensure our PR set has all existing PRs
        if (this.prList && this.prList.length > 0) {
          for (const pr of this.prList) {
            if (pr.pullRequestId) {
              this.prSet.add(pr.pullRequestId);
            }
          }
        }

        // Then filter out any duplicates from the new data
        const newPRs = (res.data || []).filter(
          (pr: any) => !this.prSet.has(pr.pullRequestId)
        );

        // Add newly loaded PRs to the set
        newPRs.forEach((pr: any) => {
          if (pr.pullRequestId) {
            this.prSet.add(pr.pullRequestId);
          }
        });

        // Fetch pipeline status for each new PR
        for (const pr of newPRs) {
          console.log(newPRs,'1473')
          // Set default values if not already set
          if (pr.pipelineStatus === undefined) {
            if (skipPipelineStatus) {
              // If we're skipping pipeline status, set a neutral display value instead of "Loading..."
              pr.pipelineStatus = 'Not loaded';
              pr.pipelineStatusDisplay = 0;
            } else {
              // Only show "Loading..." if we're actually going to load the status
              pr.pipelineStatus = 'Loading...';
              pr.pipelineStatusDisplay = 0;
            }
          }

          // Only fetch pipeline status if not skipping it and it's not already loaded or loading
          if (
            !skipPipelineStatus &&
            this.selectedProjectId &&
            this.selectedRepoId &&
            !pr.pipelineStatusLoaded &&
            !pr.loadingPipelineStatus
          ) {
            this.fetchPipelineStatus(pr);
          }
        }

        // Update the prList with new data
        this.prList = [...this.prList, ...newPRs];

        // Always update the data source with the complete prList
        this.dataSource.data = this.prList;
        // If date filters are applied, update counts from loaded data
        // if (this.hasDateFilters()) {
        //   this.countPrsByStatus(this.prList);
        // }

        // Apply current sort to the updated list if we have an active sort
        if (this.currentSort && this.currentSort.direction) {
          if (this.currentSort.active === 'timelapse') {
            this.sortTimelapseColumn(this.currentSort.direction);
          } else {
            // For other columns, let datasource handle it
            setTimeout(() => {
              this.sort.active = this.currentSort.active;
              this.sort.direction = this.currentSort.direction;
              this.dataSource.sort = this.sort;
            });
          }
        }

        this.hasMore = res.data && res.data.length === top;
        this.isLoadingMore = false;
      },
      error: (err) => {
        this.error = "Couldn't load pull requests. Please try again.";
        this.isLoadingMore = false;
        this.hasMore = false;
      },
    });
  }
  /**
   * Fetches the PR counts by status using the cached service
   * Only fetches fresh data when necessary
   */
  fetchPrStatusCounts(forceRefresh = false): void {
    if (!this.selectedProject || !this.selectedRepo || !this.patToken) {
      return;
    }

    // Set loading states
    this.loadingActiveCount = true;
    this.loadingCompletedCount = true;
    this.loadingAbandonedCount = true;

    // Use the cached service to get PR counts
    this.prDataService.getPrStatusCount(
      this.http,
      this.selectedProject,
      this.selectedRepo,
      this.patToken,
      this.apiUrlService,
      forceRefresh
    );

    // Subscribe to the observable counts
    this.prDataService.activeCount$.subscribe((count) => {
      if (count !== null) {
        this.totalCount = count;
        this.loadingActiveCount = false;

        // Once active count is loaded, we can hide the main loading indicator
        this.isLoading = false;
      }
    });

    this.prDataService.completedCount$.subscribe((count) => {
      if (count !== null) {
        this.completedCount = count;
        this.loadingCompletedCount = false;
      }
    });

    this.prDataService.abandonedCount$.subscribe((count) => {
      if (count !== null) {
        this.abandonedCount = count;
        this.loadingAbandonedCount = false;
      }
    });
  }
  onScroll() {
    if (this.virtualScroll) {
      const end = this.virtualScroll.measureScrollOffset('bottom');
      // Only load more when we're close to the bottom, have more to load, and aren't already loading
      if (end < 300 && this.hasMore && !this.isLoadingMore) {
        // Skip pipeline status if we're in search mode
        const isSearching = !!this.currentFilter;
        this.loadMorePRs(isSearching);
        // No need to fetch PR counts again when scrolling, use the cached values
      }
    }
  }
  /**
   * Count PRs by status from the loaded data
   * Used to update counts after applying date filters
   */
  countPrsByStatus(prData: any[]): void {
    if (!prData) {
      return;
    }

    // Set loading states to true while counting
    this.loadingActiveCount = true;
    this.loadingCompletedCount = true;
    this.loadingAbandonedCount = true;

    // Count PRs by status
    const activeCount = prData.filter(
      (pr) => pr.status && pr.status.toLowerCase() === 'active'
    ).length;
    const completedCount = prData.filter(
      (pr) => pr.status && pr.status.toLowerCase() === 'completed'
    ).length;
    const abandonedCount = prData.filter(
      (pr) => pr.status && pr.status.toLowerCase() === 'abandoned'
    ).length;

    // Update the counts
    this.totalCount = activeCount;
    this.completedCount = completedCount;
    this.abandonedCount = abandonedCount;

    // Hide loading states
    this.loadingActiveCount = false;
    this.loadingCompletedCount = false;
    this.loadingAbandonedCount = false;
  }
  /**
   * Check if date filters are currently applied
   * @returns true if either start or end date is set
   */
  hasDateFilters(): boolean {
    return !!(this.startDate || this.endDate);
  }

  /**
   * Sets default active comments count for pull requests without fetching pipeline status
   * @param prData Array of pull request data
   */
  fetchCommentsOnlyForPRs(prData: any[]) {
    if (!prData || prData.length === 0) return;

    // Set a default value for active comments only
    for (const pr of prData) {
      // Initialize active comments if not already set
      if (pr.activeComments === undefined) {
        pr.activeComments = 0;
      }
    }

    // Update the data source
    this.dataSource.data = [...prData];
  }

  /**
   * Force reload pipeline status for PRs that might be stuck in loading state
   * Called after data is loaded if filters were cleared
   */
  forceReloadStuckPipelineStatus(): void {
    if (!this.dataSource.data || this.dataSource.data.length === 0) return;

    let stuckCount = 0;

    for (const pr of this.dataSource.data) {
      // Check for PRs stuck in loading state or with no data
      if (
        pr.pipelineStatus === 'Loading...' ||
        pr.pipelineStatus === 'Not loaded'
      ) {
        // Reset flags
        pr.pipelineStatusLoaded = false;
        pr.loadingPipelineStatus = false;

        // Clean up any cache entry
        const cacheKey = `pipeline-${pr.pullRequestId}-${this.selectedProjectId}-${this.selectedRepoId}`;
        if (this._pipelineCallCache && this._pipelineCallCache[cacheKey]) {
          delete this._pipelineCallCache[cacheKey];
        }

        // Force reload pipeline status
        if (this.selectedProjectId && this.selectedRepoId) {
          this.fetchPipelineStatus(pr);
          stuckCount++;
        }
      }
    }
  }
}
