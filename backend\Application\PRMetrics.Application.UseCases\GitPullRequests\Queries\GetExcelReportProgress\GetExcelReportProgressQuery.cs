﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using MediatR;
using Microsoft.AspNetCore.Mvc;
using RIB.PRMetrics.Application.Dto;
using RIB.PRMetrics.Application.UseCases.Commons.Bases;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetExcelReport
{
    /// <summary>
    /// Query to retrieve the progress of multiple Excel export reports by their UUIDs.
    /// </summary>
    public class GetExcelReportProgressQuery : IRequest<BaseReponseGeneric<List<ProcessProgressDto>>>
    {
        /// <summary>
        /// Gets or sets the array of UUIDs representing the Excel export reports to query progress for.
        /// This value is required and passed as a query parameter.
        /// </summary>
        [Required(ErrorMessage = "Uuid is required.")]
        [FromQuery(Name = "uuid")]
        public string[] Uuid { get; set; }
    }
}
