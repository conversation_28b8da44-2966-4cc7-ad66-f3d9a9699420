﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using FluentValidation;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Commands.DeleteExcelReport
{
    /// <summary>
    /// Validator for <see cref="DeleteExcelReportCommand"/> to ensure the command contains valid data.
    /// </summary>
    public class TakeActionExcelReportCommandValidator : AbstractValidator<DeleteExcelReportCommand>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="TakeActionExcelReportCommandValidator"/> class.
        /// Sets up validation rules for the <see cref="DeleteExcelReportCommand"/>.
        /// </summary>
        public TakeActionExcelReportCommandValidator()
        {
            // Validate that Uuid is not empty or null
            RuleFor(x => x.Uuid)
                .NotEmpty()
                .WithMessage("Uuid is required.");
        }
    }
}
