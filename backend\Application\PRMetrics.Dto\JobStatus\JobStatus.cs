﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Application.Dto.JobStatus
{
    /// <summary>
    /// Defines constant job status values used throughout the application.
    /// These statuses represent the various states a job can be in during its lifecycle.
    /// </summary>
    public static class JobStatus
    {
        public const string Pending = "Pending";
        public const string Running = "Running";
        public const string Resume = "Resume";
        public const string Paused = "Paused";
        public const string Cancelled = "Cancelled";
        public const string Completed = "Completed";
        public const string Deleted = "Deleted";
    }
}
