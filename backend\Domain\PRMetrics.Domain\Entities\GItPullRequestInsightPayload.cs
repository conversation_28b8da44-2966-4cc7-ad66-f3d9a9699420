﻿using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;

namespace RIB.PRMetrics.Domain.Entities
{
    public class GItPullRequestInsightPayload
    {
        public List<string> PrIds { get; set; }

        /// <summary>
        /// Gets or sets the name of the Git project.
        /// This property is required for identifying the specific project in Git.
        /// </summary>
        [Required(ErrorMessage = "Project Id is required.")]
        [FromQuery(Name = "projectId")]
        public string? ProjectId { get; set; }

        /// <summary>
        /// Gets or sets the name of the Git repository.
        /// This property is required for identifying the specific repository within the project.
        /// </summary>
        [Required(ErrorMessage = "Repositories Id is required.")]
        [FromQuery(Name = "repositoryId")]
        public string? repositoryId { get; set; }

        /// <summary>
        /// Gets or sets the personal access token (PAT) for authentication.
        /// This property is required for making authorized requests to the Git repository.
        /// </summary>
        [Required(ErrorMessage = "Token is required.")]
        [FromQuery(Name = "patToken")]
        public string? PATToken { get; set; }


    }
}
