﻿using AutoMapper;
using MediatR;
using RIB.PRMetrics.Application.Dto;
using RIB.PRMetrics.Application.Interface.Persistence;
using RIB.PRMetrics.Application.UseCases.Commons.Bases;
using RIB.PRMetrics.Domain.Entities;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetGitPullRequestInsights
{
    public class GetGitPullRequestInsightsHandler : IRequestHandler<GetGitPullRequestInsightsQuery, BaseReponseGeneric<List<GitPullRequestInsightResponseDto>>>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        public GetGitPullRequestInsightsHandler(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        }

       
        public async Task<BaseReponseGeneric<List<GitPullRequestInsightResponseDto>>> Handle(GetGitPullRequestInsightsQuery request, CancellationToken cancellationToken)
        {
            var response = new BaseReponseGeneric<List<GitPullRequestInsightResponseDto>>();

            try
            {
                // Fetch pull request from the repository using provided query data
                var gitPullRequestInsights = await _unitOfWork.GitPullRequestsRepository.GitPullRequestInsightsAsync(request.encodedGitPullRequestInsightPayload);

                if (gitPullRequestInsights is not null)
                {
                    response.Data = _mapper.Map<List<GitPullRequestInsightResponseDto>>(gitPullRequestInsights);
                    response.Succcess = true;
                    response.Message = "Git Pull Request Insights  fetched successfully.";
                }
                else
                {
                    response.Succcess = false;
                    response.Message = "Pull request Insights not found.";
                }
            }
            catch (Exception ex)
            {
                response.Succcess = false;
                response.Message = $"Error occurred: {ex.Message}";
            }

            return response;
        }
    }
}
