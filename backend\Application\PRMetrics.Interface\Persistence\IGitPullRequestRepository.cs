﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
*/

using RIB.PRMetrics.Domain.Entities;


public delegate void PauseCancelCheck();
namespace RIB.PRMetrics.Application.Interface.Persistence
{
    /// <summary>
    /// Defines the contract for repository operations related to Git pull requests.
    /// This interface extends the generic repository interface to provide pull request-specific functionality.
    /// </summary>
    public interface IGitPullRequestRepository : IGenericRepository<GitPullRequest>
    {
        /// <summary>
        /// Retrieves a list of Git pull requests based on the search criteria.
        /// </summary>
        /// <param name="pullRequestSearch">An object containing the criteria to filter pull requests (e.g., status, creator, etc.).</param>
        /// <returns>A list of Git pull requests matching the search criteria.</returns>
        Task<List<GitPullRequest>> GetGitPullRequestsAsync(PullRequestSearchCriteria pullRequestSearch);

        /// <summary>
        /// Retrieves detailed information about a specific Git pull request.
        /// </summary>
        /// <param name="gitCommon">Common information (such as repository and authentication details) needed for fetching pull request data.</param>
        /// <param name="pullRequestId">The unique identifier of the pull request.</param>
        /// <returns>A detailed Git pull request object.</returns>
        Task<GitPullRequest> GetGitPullRequestDetailsAsync(GitCommon gitCommon, int pullRequestId);

        /// <summary>
        /// Determines the total number of pull requests with a specific status (e.g., active, completed, abandoned)
        /// by efficiently paging through the Azure DevOps API. Utilizes caching to optimize repeated lookups.
        /// </summary>
        /// <param name="gitCommon">Configuration for the request including project, repository, and PAT token.</param>
        /// <param name="status">The status of the pull requests to count.</param>
        /// <returns>An object containing the count of pull requests for the specified status.</returns>
        /// <exception cref="Exception">Throws if the API call fails during execution.</exception>
        Task<GitPullRequestStatusCount> GetGitPullRequestStatusCountAsync(GitCommon gitCommon, string status);


        /// <summary>
        /// Retrieves detailed information about a specific Git pull request conflicts.
        /// </summary>
        /// <param name="gitCommon">Common information (such as repository and authentication details) needed for fetching pull request data.</param>
        /// <param name="pullRequestId">The unique identifier of the pull request.</param>
        /// <returns>A detailed Git pull request object.</returns>
        Task<List<GitPullRequestConflict>> GetGitPullRequestConflictsAsync(GitCommon gitCommon, int pullRequestId);

        Task<List<GitPullRequest>> GetGitPullRequestsReportAsync(PullRequestSearchCriteria pullRequestSearch, Action<int, string, string> progressCallback = null, PauseCancelCheck? pauseCancel = null);
        Task<ExportExcelReport> GetExcelReportAsync(string Uuid);
        Task<List<ProcessProgress>> GetExcelReportProgressAsync(string[] Uuid);
        Task<ProcessProgress> DeleteExcelReportProgressAsync(string Uuid);
        Task<ProcessProgress> TakeActionOnExcelReportProgressAsync(string Uuid, string jobStatus);
        Task<List<GitPullRequestInsightResponse>> GitPullRequestInsightsAsync(string encodedGitPullRequestInsightPayload);

    }

}
