﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using RIB.PRMetrics.Application.Interface.Persistence;
using RIB.PRMetrics.Domain.Entities;
using System.Collections.Concurrent;

namespace RIB.PRMetrics.Infrastructure.Services
{
    /// <summary>
    /// Tracks delete jobs and their progress in memory.
    /// Provides thread-safe storage and retrieval of <see cref="ProcessProgress"/> entities identified by their <see cref="Guid"/> Uuid.
    /// </summary>
    public class DeleteJobTracker : IDeleteJobTracker
    {
        // Thread-safe dictionary to store the delete job progress keyed by Uuid
        private readonly ConcurrentDictionary<Guid, ProcessProgress> _jobs = new();

        /// <summary>
        /// Adds or updates the delete job progress entry in the tracker.
        /// </summary>
        /// <param name="job">The <see cref="ProcessProgress"/> instance representing the job progress to add or update.</param>
        public void Add(ProcessProgress job) => _jobs[job.Uuid] = job;

        /// <summary>
        /// Retrieves the delete job progress by its unique identifier.
        /// </summary>
        /// <param name="uuid">The unique identifier of the delete job progress to retrieve.</param>
        /// <returns>The <see cref="ProcessProgress"/> instance if found; otherwise, <c>null</c>.</returns>
        public ProcessProgress Get(Guid uuid) => _jobs.TryGetValue(uuid, out var job) ? job : null;
    }
}
