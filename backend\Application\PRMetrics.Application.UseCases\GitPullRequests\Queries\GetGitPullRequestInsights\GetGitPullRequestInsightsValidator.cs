﻿using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetGitPullRequestInsights
{
    public class GetGitPullRequestInsightsValidator : AbstractValidator<GetGitPullRequestInsightsQuery>
    {
        public GetGitPullRequestInsightsValidator()
        {
            // Ensure 'encodedGitPullRequestInsightPayload' field is not empty or null
            RuleFor(x => x.encodedGitPullRequestInsightPayload)
                .NotEmpty().WithMessage("encodedGitPullRequestInsightPayload is required.")
                .NotNull().WithMessage("encodedGitPullRequestInsightPayload must not be null.");
        }
    }
}
