﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using RIB.PRMetrics.Domain.Entities;

namespace RIB.PRMetrics.Application.Interface.Persistence
{
    /// <summary>
    /// Defines the contract for tracking and managing the lifecycle of export jobs.
    /// Allows retrieval, addition, and control operations like pause, resume, and cancel on jobs.
    /// </summary>
    public interface IJobTracker
    {
        /// <summary>
        /// Retrieves the export job identified by the specified UUID.
        /// </summary>
        /// <param name="uuid">The unique identifier of the export job.</param>
        /// <returns>The <see cref="ExportJob"/> corresponding to the given UUID.</returns>
        ExportJob Get(Guid uuid);

        /// <summary>
        /// Adds a new export job to the tracker.
        /// </summary>
        /// <param name="job">The <see cref="ExportJob"/> to add.</param>
        void Add(ExportJob job);

        /// <summary>
        /// Pauses the export job identified by the specified UUID.
        /// </summary>
        /// <param name="uuid">The unique identifier of the export job to pause.</param>
        /// <param name="process">Optional process progress information.</param>
        void Pause(Guid uuid, ProcessProgress process = null);

        /// <summary>
        /// Resumes the export job identified by the specified UUID.
        /// </summary>
        /// <param name="uuid">The unique identifier of the export job to resume.</param>
        /// <param name="process">Optional process progress information.</param>
        void Resume(Guid uuid, ProcessProgress process = null);

        /// <summary>
        /// Cancels the export job identified by the specified UUID.
        /// </summary>
        /// <param name="uuid">The unique identifier of the export job to cancel.</param>
        /// <param name="process">Optional process progress information.</param>
        void Cancel(Guid uuid, ProcessProgress process = null);
    }
}
