﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using RIB.PRMetrics.Application.Interface.Persistence;
using System.Collections.Concurrent;

namespace RIB.PRMetrics.Infrastructure.BackgroundJobs
{
    /// <summary>
    /// Background service that continuously processes jobs from a job queue.
    /// It dequeues jobs and executes them asynchronously using an injected job service.
    /// Uses dependency injection scope for each job execution and tracks running jobs in a concurrent dictionary.
    /// Allows multiple jobs to run concurrently without awaiting immediately, supporting graceful shutdown by awaiting all running jobs.
    /// </summary>
    public class JobWorker : BackgroundService
    {
        private readonly ILogger<JobWorker> _logger;
        private readonly IJobQueue _jobQueue;
        private readonly IServiceProvider _serviceProvider;

        // Optional: limit max parallel jobs to avoid resource exhaustion
        private readonly SemaphoreSlim _concurrencyLimiter = new SemaphoreSlim(5); // Adjust max concurrency as needed

        public JobWorker(ILogger<JobWorker> logger, IJobQueue jobQueue, IServiceProvider serviceProvider)
        {
            _logger = logger;
            _jobQueue = jobQueue;
            _serviceProvider = serviceProvider;
        }
        private readonly ConcurrentDictionary<Guid, Task> _runningJobs = new();

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                if (_jobQueue.TryDequeue(out var job))
                {
                    using var scope = _serviceProvider.CreateScope();
                    var jobService = scope.ServiceProvider.GetRequiredService<IJobService>();

                    // Start job asynchronously without awaiting to allow concurrency
                    var task = Task.Run(() => jobService.ExecuteAsync(job), stoppingToken);

                    _runningJobs[job.Uuid] = task;

                    // Optionally, remove completed tasks from _runningJobs here or periodically
                }
                else
                {
                    await Task.Delay(500, stoppingToken);
                }
            }

            // Optionally wait for all running jobs on shutdown:
            await Task.WhenAll(_runningJobs.Values);
        }


    }
}
