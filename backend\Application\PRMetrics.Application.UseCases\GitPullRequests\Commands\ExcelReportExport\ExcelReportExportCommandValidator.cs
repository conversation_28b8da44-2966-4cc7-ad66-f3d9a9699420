﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using FluentValidation;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Commands.ExcelReportExport
{
    /// <summary>
    /// Validator for <see cref="ExcelReportExportCommand"/> to ensure required fields are provided.
    /// </summary>
    public class ExcelReportExportCommandValidator : AbstractValidator<ExcelReportExportCommand>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ExcelReportExportCommandValidator"/> class,
        /// defining validation rules for the export command.
        /// </summary>
        public ExcelReportExportCommandValidator()
        {
            // Validate that Project is not empty or null
            RuleFor(x => x.Project)
                .NotEmpty()
                .WithMessage("Project is required.");

            // Validate that Repositories is not empty or null
            RuleFor(x => x.Repositories)
                .NotEmpty()
                .WithMessage("Repositories are required.");

            // You can add more validation rules based on other properties within PullRequestSearchCriteria
            // For example, if you want to validate date ranges, state filters, etc.
        }
    }
}
