﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using RIB.PRMetrics.Application.Interface.Persistence;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace RIB.PRMetrics.Infrastructure.BackgroundJobs
{
    public class FileCleanupWorker : BackgroundService
    {
        private readonly ILogger<FileCleanupWorker> _logger;
        private readonly IDeleteJobQueue _queue;
        private readonly IMemoryCache _cache;

        // Limit max parallel deletes, adjust concurrency based on I/O
        private readonly SemaphoreSlim _concurrencyLimiter = new SemaphoreSlim(3);

        public FileCleanupWorker(IDeleteJobQueue queue, IMemoryCache cache, ILogger<FileCleanupWorker> logger)
        {
            _queue = queue;
            _cache = cache;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("FileCleanupWorker started.");

            while (!stoppingToken.IsCancellationRequested)
            {
                if (_queue.TryDequeue(out var job))
                {
                    // Fire and forget deletion task with concurrency control
                    _ = Task.Run(async () =>
                    {
                        await _concurrencyLimiter.WaitAsync(stoppingToken);
                        try
                        {
                            // If FileUrl missing, construct default path inside TempFiles folder
                            if (string.IsNullOrWhiteSpace(job.FileUrl))
                            {
                                var tempFolder = Path.Combine(Directory.GetCurrentDirectory(), "TempFiles");
                                if (!Directory.Exists(tempFolder))
                                    Directory.CreateDirectory(tempFolder);

                                job.FileUrl = Path.Combine(tempFolder, job.FileName);
                            }

                            if (!string.IsNullOrEmpty(job.FileUrl) && File.Exists(job.FileUrl))
                            {
                                File.Delete(job.FileUrl);
                                _logger.LogInformation("Deleted file: {Path}", job.FileUrl);
                            }
                            else
                            {
                                _logger.LogWarning("File not found or path empty: {Path}", job.FileUrl);
                            }

                            // Remove from cache
                            _cache.Remove(job.Uuid.ToString());
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error deleting file {Path}", job.FileUrl);
                        }
                        finally
                        {
                            _concurrencyLimiter.Release();
                        }
                    }, stoppingToken);
                }
                else
                {
                    // No jobs in queue, wait a bit before checking again
                    await Task.Delay(1000, stoppingToken);
                }
            }

            _logger.LogInformation("FileCleanupWorker stopped.");
        }
    }
}
