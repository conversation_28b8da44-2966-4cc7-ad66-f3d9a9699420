﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using RIB.PRMetrics.Application.Interface.Persistence;
using RIB.PRMetrics.Domain.Entities;
using System.Collections.Concurrent;

namespace RIB.PRMetrics.Infrastructure.Services
{
    /// <summary>
    /// An in-memory implementation of <see cref="IDeleteJobQueue"/> that schedules and dequeues delete jobs.
    /// Jobs are stored in a thread-safe dictionary with a scheduled execution time.
    /// </summary>
    public class InMemoryDeleteJobQueue : IDeleteJobQueue
    {
        // Internal thread-safe dictionary storing jobs with their scheduled execution time.
        // Key: job's UUID, Value: tuple containing the job and its scheduled execution time.
        private readonly ConcurrentDictionary<Guid, (ProcessProgress Job, DateTime ScheduledTime)> _jobs =
            new ConcurrentDictionary<Guid, (ProcessProgress, DateTime)>();

        /// <summary>
        /// Schedules a delete job to be executed after a specified delay.
        /// Adds or updates the job with its scheduled execution time.
        /// </summary>
        /// <param name="job">The <see cref="ProcessProgress"/> job to schedule.</param>
        /// <param name="delay">The delay after which the job becomes eligible for dequeue.</param>
        public void Schedule(ProcessProgress job, TimeSpan delay)
        {
            var scheduledTime = DateTime.UtcNow.Add(delay);
            _jobs[job.Uuid] = (job, scheduledTime); // Adds or updates the job
        }

        /// <summary>
        /// Attempts to dequeue the next scheduled job whose scheduled time has passed.
        /// Removes the job from the queue to avoid duplicate processing.
        /// </summary>
        /// <param name="job">When this method returns, contains the dequeued job if successful; otherwise, null.</param>
        /// <returns><c>true</c> if a job was dequeued successfully; otherwise, <c>false</c>.</returns>
        public bool TryDequeue(out ProcessProgress job)
        {
            var now = DateTime.UtcNow;

            foreach (var entry in _jobs)
            {
                var uuid = entry.Key;
                var (queuedJob, scheduledTime) = entry.Value;

                if (now >= scheduledTime)
                {
                    // Attempt to remove the job to ensure only one thread dequeues it
                    if (_jobs.TryRemove(uuid, out var removed))
                    {
                        job = removed.Job;
                        return true;
                    }
                }
            }

            job = null;
            return false;
        }
    }
}
