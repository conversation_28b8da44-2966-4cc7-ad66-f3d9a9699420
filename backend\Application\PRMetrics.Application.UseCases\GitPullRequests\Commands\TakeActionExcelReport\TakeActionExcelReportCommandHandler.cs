﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using AutoMapper;
using MediatR;
using RIB.PRMetrics.Application.Dto;
using RIB.PRMetrics.Application.Interface.Persistence;
using RIB.PRMetrics.Application.UseCases.Commons.Bases;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Commands.TakeActionExcelReport
{
    /// <summary>
    /// Handles the <see cref="TakeActionExcelReportCommand"/> to perform actions like Pause, Resume, or Cancel
    /// on an Excel report export job.
    /// </summary>
    public class TakeActionExcelReportCommandHandler : IRequestHandler<TakeActionExcelReportCommand, BaseReponseGeneric<ProcessProgressDto>>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        /// <summary>
        /// Initializes a new instance of the <see cref="TakeActionExcelReportCommandHandler"/> class.
        /// </summary>
        /// <param name="unitOfWork">Unit of work for repository access.</param>
        /// <param name="mapper">AutoMapper instance for object mapping.</param>
        public TakeActionExcelReportCommandHandler(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        /// <summary>
        /// Handles the command asynchronously by applying the specified action on the Excel export job
        /// and returning the updated progress information.
        /// </summary>
        /// <param name="request">The command containing the job UUID and the desired job status.</param>
        /// <param name="cancellationToken">Cancellation token.</param>
        /// <returns>A response containing the updated <see cref="ProcessProgressDto"/>.</returns>
        public async Task<BaseReponseGeneric<ProcessProgressDto>> Handle(TakeActionExcelReportCommand request, CancellationToken cancellationToken)
        {
            var response = new BaseReponseGeneric<ProcessProgressDto>();

            try
            {
                var excelExportReport = await _unitOfWork.GitPullRequestsRepository
                    .TakeActionOnExcelReportProgressAsync(request.Uuid, request.JobStatus);

                if (excelExportReport is not null)
                {
                    response.Data = _mapper.Map<ProcessProgressDto>(excelExportReport);
                    response.Succcess = true;
                    response.Message = $"Excel Export File {request.JobStatus} successfully.";
                }
                else
                {
                    response.Succcess = false;
                    response.Message = "Excel Export File not found.";
                }
            }
            catch (Exception ex)
            {
                response.Succcess = false;
                response.Message = $"Error occurred: {ex.Message}";
            }

            return response;
        }
    }
}
