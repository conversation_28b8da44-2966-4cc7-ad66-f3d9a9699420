﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using AutoMapper;
using MediatR;
using RIB.PRMetrics.Application.Dto;
using RIB.PRMetrics.Application.Interface.Persistence;
using RIB.PRMetrics.Application.UseCases.Commons.Bases;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Commands.DeleteExcelReport
{
    /// <summary>
    /// Handles the deletion of an Excel report by processing the <see cref="DeleteExcelReportCommand"/>.
    /// </summary>
    public class TakeActionExcelReportCommandHandler : IRequestHandler<DeleteExcelReportCommand, BaseReponseGeneric<ProcessProgressDto>>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        /// <summary>
        /// Initializes a new instance of the <see cref="TakeActionExcelReportCommandHandler"/> class.
        /// </summary>
        /// <param name="unitOfWork">The unit of work for repository access.</param>
        /// <param name="mapper">The AutoMapper instance for object mapping.</param>
        public TakeActionExcelReportCommandHandler(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        /// <summary>
        /// Handles the <see cref="DeleteExcelReportCommand"/> asynchronously,
        /// attempting to delete the specified Excel report and returning the operation result.
        /// </summary>
        /// <param name="request">The command containing the UUID of the report to delete.</param>
        /// <param name="cancellationToken">A token to monitor for cancellation requests.</param>
        /// <returns>A <see cref="BaseReponseGeneric{ProcessProgressDto}"/> indicating success or failure of the deletion.</returns>
        public async Task<BaseReponseGeneric<ProcessProgressDto>> Handle(DeleteExcelReportCommand request, CancellationToken cancellationToken)
        {
            var response = new BaseReponseGeneric<ProcessProgressDto>();

            try
            {
                var excelExportReport = await _unitOfWork.GitPullRequestsRepository.DeleteExcelReportProgressAsync(request.Uuid);

                if (excelExportReport is not null)
                {
                    response.Data = _mapper.Map<ProcessProgressDto>(excelExportReport);
                    response.Succcess = true;
                    response.Message = "Excel Export File Deleted successfully.";
                }
                else
                {
                    response.Succcess = false;
                    response.Message = "Excel Export File not found.";
                }
            }
            catch (Exception ex)
            {
                response.Succcess = false;
                response.Message = $"Error occurred: {ex.Message}";
            }

            return response;
        }
    }
}
