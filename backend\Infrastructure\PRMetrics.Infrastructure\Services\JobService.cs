﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using ClosedXML.Excel;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Caching.Memory;
using RIB.PRMetrics.Application.Interface.Persistence;
using RIB.PRMetrics.Domain;
using RIB.PRMetrics.Domain.Entities;
using RIB.PRMetrics.Domain.Entities.JobStatus;
using RIB.PRMetrics.Infrastructure.SignalR;
using System.Collections.Concurrent;

namespace RIB.PRMetrics.Infrastructure.Services
{
    /// <summary>
    /// Service responsible for executing export jobs that generate pull request metrics reports.
    /// It orchestrates data retrieval from multiple repositories, processes the data, generates Excel reports,
    /// tracks job progress, and broadcasts updates via SignalR.
    /// </summary>
    public class JobService : IJobService
    {
        // Dependencies for caching, SignalR communication, and data repositories
        private readonly IMemoryCache _cache;
        private readonly IHubContext<ProgressHub> _hub;
        private readonly IGitPullRequestRepository _gitPullRequestRepository;
        private readonly IGitPullRequestThreadsRepository _gitPullRequestThreadsRepository;
        private readonly IContributionsHierarchyQueryRepository _contributionsHierarchyQueryRepository;
        private readonly IGitPullRequestBuildTimeLineRepository _gitPullRequestBuildTimeLineRepository;
        private readonly IDeleteJobQueue _deleteJobQueue;
        private readonly IJobTracker _jobTracker;

        /// <summary>
        /// Constructor injecting all required dependencies for data access, caching, and communication.
        /// </summary>
        public JobService(
            IMemoryCache cache,
            IHubContext<ProgressHub> hub,
            IGitPullRequestRepository gitPullRequestRepository,
            IGitPullRequestThreadsRepository gitPullRequestThreadsRepository,
            IContributionsHierarchyQueryRepository contributionsHierarchyQueryRepository,
            IGitPullRequestBuildTimeLineRepository gitPullRequestBuildTimeLineRepository,
            IDeleteJobQueue deleteJobQueue,
            IJobTracker jobTracker)
        {
            _cache = cache;
            _hub = hub;
            _gitPullRequestRepository = gitPullRequestRepository;
            _gitPullRequestThreadsRepository = gitPullRequestThreadsRepository;
            _contributionsHierarchyQueryRepository = contributionsHierarchyQueryRepository;
            _gitPullRequestBuildTimeLineRepository = gitPullRequestBuildTimeLineRepository;
            _deleteJobQueue = deleteJobQueue;
            _jobTracker = jobTracker;
        }

        /// <summary>
        /// Updates the progress status of a job by caching progress information and broadcasting it via SignalR.
        /// </summary>
        private void UpdateProgress(ExportJob job, int percent, string status, string jobStatus, string fileUrl = "")
        {
            var progress = new ProcessProgress
            {
                Uuid = job.Uuid,
                Percentage = percent,
                Status = status,
                FileName = job.FileName,
                JobStatus = jobStatus,
                FileUrl = fileUrl
            };

            _cache.Set(job.Uuid.ToString(), progress);
            progress.FileUrl = ""; // Clear FileUrl to avoid leaking path info to clients unless specified
            _hub.Clients.All.SendAsync("ReceiveProgress", progress);
        }

        /// <summary>
        /// Entry point for executing the export job asynchronously.
        /// Sets job status to running, tracks it, and starts the data preparation process.
        /// </summary>
        public async Task ExecuteAsync(ExportJob job)
        {
            job.Status = JobStatus.Running;
            _jobTracker.Add(job);
            UpdateProgress(job, 0, "Started", JobStatus.Pending);
            await PreparedReportDataAsync(job);
        }

        /// <summary>
        /// Prepares and fetches all required pull request data asynchronously.
        /// It fetches PR data, related comments, contributions, and policies in batches,
        /// reporting progress throughout the process.
        /// Also handles pause/cancel checks at multiple points to allow graceful interruption.
        /// </summary>
        private async Task PreparedReportDataAsync(ExportJob job)
        {
            var gitCommon = new GitCommon
            {
                PATToken = job.pullRequestSearchCriteria.PATToken,
                Project = job.pullRequestSearchCriteria.Project,
                Repositories = job.pullRequestSearchCriteria.Repositories,
            };

            CheckPauseCancel(job);
            UpdateProgress(job, 4, "Prepare Payload", JobStatus.Running);
            CheckPauseCancel(job);

            var prList = await _gitPullRequestRepository.GetGitPullRequestsReportAsync(
                job.pullRequestSearchCriteria,
                (percent, status, jobStatus) => UpdateProgress(job, percent, status, jobStatus),
                () => CheckPauseCancel(job));

            CheckPauseCancel(job);

            var mainReportData = new ConcurrentBag<ExcelReport>();
            int total = prList.Count;
            int batchSize = 5;

            for (int i = 0; i < total; i += batchSize)
            {
                CheckPauseCancel(job);

                var batch = prList.Skip(i).Take(batchSize).ToList();
                var tasks = batch.Select(async (pr, indexInBatch) =>
                {
                    CheckPauseCancel(job);

                    var threadTask = _gitPullRequestThreadsRepository.GetGitPullRequestThreadsAsync(gitCommon, pr.PullRequestId, pr.CreatedBy.Id);
                    var contributionTask = _contributionsHierarchyQueryRepository.GetGitPullRequestContributionsHierarchyQueryAsync(
                        pr.PullRequestId.ToString(),
                        pr.Repository.Id,
                        pr.Repository.Project.Id,
                        gitCommon.PATToken);

                    await Task.WhenAll(threadTask, contributionTask);

                    var thread = await threadTask;
                    var contribution = await contributionTask;

                    var policy = contribution?.DataProviders?.prDetailDataProvider?.Policies?
                        .FirstOrDefault(x => !string.IsNullOrEmpty(x.DisplayName) && x.DisplayName.Contains("CI must pass"));

                    mainReportData.Add(new ExcelReport
                    {
                        PullRequestId = pr.PullRequestId,
                        Title = pr.Title,
                        Status = pr.Status,
                        Project = pr.Repository.Project.Name,
                        Repository = pr.Repository.Name,
                        Author = pr.CreatedBy.DisplayName,
                        SourceBranch = pr.SourceRefName.Replace("refs/heads/", ""),
                        Reviewer = string.Join(", ", pr.Reviewers.Select(x => x.DisplayName.Replace("[itwo40]\\", ""))),
                        CreationDate = pr.CreationDate,
                        ActiveComment = thread.commentThread.CommentThreads.Count(x => x.Status == "active"),
                        ResolvedComment = thread.commentThread.CommentThreads.Count(x => x.Status == "fixed"),
                        TotalComment = thread.commentThread.CommentThreads.Count(x => x.Status == "active" || x.Status == "fixed"),
                        PipelineStatus = policy?.DisplayText,
                        PipelineDetails = policy != null ? $"https://dev.azure.com/ribdev/{pr.Repository.Project.Name}/_build/results?buildId={policy.BuildId}&view=results" : null
                    });

                    var current = Math.Min(i + indexInBatch + 1, total);
                    int progress = 30 + (current * 20 / total);
                    UpdateProgress(job, progress, "Processing Pull Requests", job.Status);
                });

                await Task.WhenAll(tasks);
            }

            UpdateProgress(job, 50, "Prepare Excel Report Data", JobStatus.Running);
            await ExportToExcelAsync(job, mainReportData.ToList());

            CheckPauseCancel(job);
        }

        /// <summary>
        /// Generates an Excel report using ClosedXML based on the prepared pull request data.
        /// It writes headers and rows, applies hyperlinks, updates progress,
        /// and schedules the generated file for deletion after 1 day.
        /// </summary>
        public async Task ExportToExcelAsync(ExportJob job, IEnumerable<ExcelReport> reportdata)
        {
            CheckPauseCancel(job);

            using var workbook = new XLWorkbook();
            var worksheet = workbook.Worksheets.Add($"PR_Metrics_{DateTime.Now:dd_MM_yyyy_HH_mm_ss}");
            UpdateProgress(job, 55, "Generating Worksheet", JobStatus.Running);

            string[] headers = new[]
            {
                "Sr.No.", "Pull Request Id", "Title", "Created Date", "Active Comment", "Resolved Comment",
                "Total Comment", "Status", "Author", "Reviewer", "Source Branch", "Project", "Repository", "Pipeline Status", "Pipeline Details"
            };

            CheckPauseCancel(job);

            for (int i = 0; i < headers.Length; i++)
                worksheet.Cell(1, i + 1).Value = headers[i];

            for (int i = 0; i < reportdata.Count(); i++)
            {
                CheckPauseCancel(job);

                var report = reportdata.ElementAt(i);
                worksheet.Cell(i + 2, 1).Value = i + 1;

                var prcolcell = worksheet.Cell(i + 2, 2);
                prcolcell.Value = report.PullRequestId.ToString();
                prcolcell.SetHyperlink(new XLHyperlink($"https://dev.azure.com/ribdev/{report.Project}/_git/{report.Repository}/pullrequest/{report.PullRequestId}"));
                prcolcell.Style.Font.Underline = XLFontUnderlineValues.Single;
                prcolcell.Style.Font.FontColor = XLColor.Blue;

                worksheet.Cell(i + 2, 3).Value = report.Title;
                worksheet.Cell(i + 2, 4).Value = report.CreationDate;
                worksheet.Cell(i + 2, 5).Value = report.ActiveComment;
                worksheet.Cell(i + 2, 6).Value = report.ResolvedComment;
                worksheet.Cell(i + 2, 7).Value = report.TotalComment;
                worksheet.Cell(i + 2, 8).Value = report.Status;
                worksheet.Cell(i + 2, 9).Value = report.Author;
                worksheet.Cell(i + 2, 10).Value = report.Reviewer;
                worksheet.Cell(i + 2, 11).Value = report.SourceBranch;
                worksheet.Cell(i + 2, 12).Value = report.Project;
                worksheet.Cell(i + 2, 13).Value = report.Repository;
                worksheet.Cell(i + 2, 14).Value = report.PipelineStatus;

                if (!string.IsNullOrEmpty(report.PipelineDetails))
                {
                    var buildDetailscell = worksheet.Cell(i + 2, 15);
                    buildDetailscell.Value = "Details";
                    buildDetailscell.SetHyperlink(new XLHyperlink(report.PipelineDetails));
                    buildDetailscell.Style.Font.Underline = XLFontUnderlineValues.Single;
                    buildDetailscell.Style.Font.FontColor = XLColor.Blue;
                }
                else
                {
                    worksheet.Cell(i + 2, 15).Value = report.PipelineDetails;
                }

                if ((i + 1) % (reportdata.Count() / 4) == 0)
                    UpdateProgress(job, 60 + ((i + 1) * 30 / reportdata.Count()), "Writing Excel Rows", JobStatus.Running);
            }

            worksheet.RangeUsed().CreateTable();

            CheckPauseCancel(job);

            var tempFolder = Path.Combine(Directory.GetCurrentDirectory(), "TempFiles");
            if (!Directory.Exists(tempFolder)) Directory.CreateDirectory(tempFolder);
            var filePath = Path.Combine(tempFolder, $"{job.FileName}");
            if (!string.IsNullOrEmpty(filePath))
            {
                workbook.SaveAs(filePath);

                UpdateProgress(job, 95, "Excel Generated", JobStatus.Running, filePath);
                UpdateProgress(job, 100, "Completed", JobStatus.Completed, filePath);

                // Schedule the generated file to be deleted after 1 day
                _deleteJobQueue.Schedule(new ProcessProgress
                {
                    Uuid = job.Uuid,
                    FileName = job.FileName,
                    FileUrl = filePath
                }, TimeSpan.FromDays(1));
            }
        }

        /// <summary>
        /// Checks if the job is requested to pause or cancel.
        /// Throws an OperationCanceledException if cancellation is requested,
        /// or waits if paused.
        /// </summary>
        private void CheckPauseCancel(ExportJob job)
        {
            job.TokenSource.Token.ThrowIfCancellationRequested();
            job.PauseHandle.Wait();
        }
    }
}
