﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Domain.Entities.JobStatus
{
    /// <summary>
    /// Contains string constants representing various job statuses.
    /// </summary>
    public static class JobStatus
    {
        /// <summary>
        /// Job is pending and has not started yet.
        /// </summary>
        public const string Pending = "Pending";

        /// <summary>
        /// Job is currently running.
        /// </summary>
        public const string Running = "Running";

        /// <summary>
        /// Job is resumed after being paused.
        /// </summary>
        public const string Resume = "Resume";

        /// <summary>
        /// Job is paused and temporarily stopped.
        /// </summary>
        public const string Paused = "Paused";

        /// <summary>
        /// Job has been cancelled.
        /// </summary>
        public const string Cancelled = "Cancelled";

        /// <summary>
        /// Job has been completed successfully.
        /// </summary>
        public const string Completed = "Completed";

        /// <summary>
        /// Job has been deleted.
        /// </summary>
        public const string Deleted = "Deleted";
    }
}
