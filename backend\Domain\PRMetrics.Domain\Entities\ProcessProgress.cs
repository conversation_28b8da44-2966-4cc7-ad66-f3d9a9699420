﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Domain.Entities
{
    /// <summary>
    /// Represents the progress state of an ongoing process, such as an export job.
    /// </summary>
    public class ProcessProgress
    {
        /// <summary>
        /// Gets or sets the unique identifier for the process.
        /// </summary>
        public Guid Uuid { get; set; }

        /// <summary>
        /// Gets or sets the completion percentage of the process.
        /// Defaults to 0.
        /// </summary>
        public int Percentage { get; set; } = 0;

        /// <summary>
        /// Gets or sets the current status message of the process.
        /// Defaults to "Pending".
        /// </summary>
        public string Status { get; set; } = "Pending";

        /// <summary>
        /// Gets or sets the file name associated with the process.
        /// Defaults to an empty string.
        /// </summary>
        public string FileName { get; set; } = "";

        /// <summary>
        /// Gets or sets the URL where the file can be accessed.
        /// </summary>
        public string FileUrl { get; set; }

        /// <summary>
        /// Gets or sets the job status of the process.
        /// Defaults to "Pending".
        /// </summary>
        public string JobStatus { get; set; } = "Pending";
    }
}
