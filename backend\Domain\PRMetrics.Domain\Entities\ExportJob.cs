﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Domain.Entities
{
    /// <summary>
    /// Represents an export job with its unique identifier, file information, status, 
    /// and controls for cancellation and pausing.
    /// </summary>
    public class ExportJob
    {
        /// <summary>
        /// Gets or sets the unique identifier for the export job.
        /// </summary>
        public Guid Uuid { get; set; }

        /// <summary>
        /// Gets or sets the name of the file being exported.
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// Gets or sets the pull request search criteria associated with this export job.
        /// </summary>
        public PullRequestSearchCriteria pullRequestSearchCriteria { get; set; } = null;

        /// <summary>
        /// Gets or sets the current status of the job (e.g., "Pending", "Running").
        /// Default is "Pending".
        /// </summary>
        public string Status { get; set; } = "Pending";

        /// <summary>
        /// Gets or sets the token source used to cancel the job.
        /// </summary>
        public CancellationTokenSource TokenSource { get; set; } = new();

        /// <summary>
        /// Gets or sets the synchronization primitive used to pause and resume the job.
        /// Initially set to signaled (running).
        /// </summary>
        public ManualResetEventSlim PauseHandle { get; set; } = new(true);
    }
}
