﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using MediatR;
using Microsoft.AspNetCore.Mvc;
using RIB.PRMetrics.Application.Dto;
using RIB.PRMetrics.Application.UseCases.Commons.Bases;
using System.ComponentModel.DataAnnotations;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Commands.DeleteExcelReport
{
    /// <summary>
    /// Command to request deletion of an Excel report identified by a UUID.
    /// </summary>
    public class DeleteExcelReportCommand : IRequest<BaseReponseGeneric<ProcessProgressDto>>
    {
        /// <summary>
        /// Gets or sets the UUID of the Excel report to be deleted.
        /// This value is required and is expected as a query parameter.
        /// </summary>
        [Required(ErrorMessage = "uuid is required.")]
        [FromQuery(Name = "uuid")]
        public string Uuid { get; set; }
    }
}
