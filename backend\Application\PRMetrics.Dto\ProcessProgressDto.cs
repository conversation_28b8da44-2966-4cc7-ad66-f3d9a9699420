﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Application.Dto
{
    /// <summary>
    /// Represents the progress details of a background process or job,
    /// including its status, completion percentage, and associated file information.
    /// </summary>
    public class ProcessProgressDto
    {
        /// <summary>
        /// Gets or sets the unique identifier for the process instance.
        /// </summary>
        public Guid Uuid { get; set; }

        /// <summary>
        /// Gets or sets the percentage of completion for the process (0 to 100).
        /// </summary>
        public int Percentage { get; set; } = 0;

        /// <summary>
        /// Gets or sets the current general status of the process (e.g., "Pending", "Running").
        /// </summary>
        public string Status { get; set; } = "Pending";

        /// <summary>
        /// Gets or sets the specific job status (e.g., "Pending", "Completed", etc.).
        /// </summary>
        public string JobStatus { get; set; } = "Pending";

        /// <summary>
        /// Gets or sets the name of the file associated with the process, if any.
        /// </summary>
        public string FileName { get; set; } = "";

        /// <summary>
        /// Gets or sets the URL where the generated file can be accessed.
        /// </summary>
        public string FileUrl { get; set; }
    }
}
