.download-ui-container {
  width: 100%;
  max-width: 500px;
}

.download-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.download-content,
.download-inline {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.download-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
}

.download-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0; // Allow text to truncate
}

.status-icon {
  font-size: 24px;
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

.download-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 0; // Allow text to truncate
}

.file-name {
  font-weight: 500;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.87);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.status-text {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.download-actions {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
}

.progress-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.download-progress {
  height: 6px;
  border-radius: 3px;
  overflow: hidden;
}

.progress-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
  gap: 8px;
}

.progress-percentage {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.87);
}

.progress-info {
  flex: 1;
  text-align: center;
}

.download-speed {
  text-align: right;
}

.completion-message,
.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
}

.completion-message {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.error-message {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
  border: 1px solid rgba(244, 67, 54, 0.3);
}

// Responsive design
@media (max-width: 600px) {
  .download-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .download-info {
    justify-content: center;
  }

  .download-actions {
    justify-content: center;
  }

  .progress-details {
    flex-direction: column;
    align-items: center;
    gap: 4px;
  }

  .progress-info,
  .download-speed {
    text-align: center;
  }
}

// Animation for progress bar
.download-progress {
  transition: all 0.3s ease;
}

// Hover effects for buttons
.download-actions button {
  transition: all 0.2s ease;
  
  &:hover {
    transform: scale(1.05);
  }
}

// Status icon animations
.status-icon {
  transition: all 0.3s ease;
}

// Pulse animation for downloading status
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

.download-info .status-icon {
  &.mat-primary {
    animation: pulse 2s infinite;
  }
}

// Success animation
@keyframes checkmark {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

.completion-message .mat-icon {
  animation: checkmark 0.5s ease-out;
}

// Error shake animation
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-4px);
  }
  75% {
    transform: translateX(4px);
  }
}

.error-message {
  animation: shake 0.5s ease-out;
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .file-name {
    color: rgba(255, 255, 255, 0.87);
  }

  .status-text {
    color: rgba(255, 255, 255, 0.6);
  }

  .progress-details {
    color: rgba(255, 255, 255, 0.6);
  }

  .progress-percentage {
    color: rgba(255, 255, 255, 0.87);
  }

  .completion-message {
    background-color: rgba(76, 175, 80, 0.2);
    border-color: rgba(76, 175, 80, 0.4);
  }

  .error-message {
    background-color: rgba(244, 67, 54, 0.2);
    border-color: rgba(244, 67, 54, 0.4);
  }
}
