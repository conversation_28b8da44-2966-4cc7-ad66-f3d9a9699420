﻿using MediatR;
using RIB.PRMetrics.Application.Dto;
using RIB.PRMetrics.Application.UseCases.Commons.Bases;
using System.ComponentModel.DataAnnotations;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetGitPullRequestInsights
{
    public class GetGitPullRequestInsightsQuery : IRequest<BaseReponseGeneric<List<GitPullRequestInsightResponseDto>>>
    {
        /// <summary>
        /// encodedGitPullRequestInsightPayload Required field.
        /// </summary>
        [Required(ErrorMessage = "encodedGitPullRequestInsightPayload is required.")]
        public string encodedGitPullRequestInsightPayload { get; set; } 
    }
}
