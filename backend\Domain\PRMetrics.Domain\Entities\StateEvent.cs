﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Domain.Entities
{
    /// <summary>
    /// Represents a state change event with timing information and intervals.
    /// </summary>
    public class StateEvent
    {
        /// <summary>
        /// Gets or sets the timestamp when the state event occurred.
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// Gets or sets the state of the event, e.g., "draft" or "published".
        /// </summary>
        public string State { get; set; }

        /// <summary>
        /// Gets or sets the time interval associated with the state event.
        /// </summary>
        public TimeSpan Interval { get; set; }

        /// <summary>
        /// Gets or sets a string representation or description of the interval content.
        /// </summary>
        public string IntervalContent { get; set; }

        /// <summary>
        /// Gets or sets the date when the next state update is expected.
        /// </summary>
        public DateTime NextStateUpdatedDate { get; set; }
    }
}
