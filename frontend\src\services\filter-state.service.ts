import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export interface FilterState {
  searchFilter: string;
  statusFilter: string;
  startDate: Date | null;
  endDate: Date | null;
}

export interface ActiveFilters {
  hasSearch: boolean;
  hasStatus: boolean;
  hasDates: boolean;
  searchDisplay: string;
  statusDisplay: string;
  dateDisplay: string;
}

/**
 * Service to maintain filter state across page navigation
 */
@Injectable({
  providedIn: 'root',
})
export class FilterStateService {
  // Default filter state
  private defaultState: FilterState = {
    searchFilter: '',
    statusFilter: 'all',
    startDate: null,
    endDate: null,
  };

  // Current filter state
  private filterStateSubject = new BehaviorSubject<FilterState>(
    this.defaultState
  );
  public filterState$ = this.filterStateSubject.asObservable();

  // Active filters subject for reactive UI updates
  private activeFiltersSubject = new BehaviorSubject<ActiveFilters>({
    hasSearch: false,
    hasStatus: false,
    hasDates: false,
    searchDisplay: '',
    statusDisplay: '',
    dateDisplay: '',
  });
  public activeFilters$ = this.activeFiltersSubject.asObservable();

  constructor() {}
  /**
   * Update the filter state
   * @param state Partial filter state to update
   * @param forceUpdate Force an update even if values haven't changed
   */
  updateFilterState(
    state: Partial<FilterState>,
    forceUpdate: boolean = false
  ): void {
    const currentState = this.filterStateSubject.value;
    const newState = {
      ...currentState,
      ...state,
    };

    // Check if there's actually a change
    const hasChanges =
      forceUpdate ||
      newState.searchFilter !== currentState.searchFilter ||
      newState.statusFilter !== currentState.statusFilter ||
      newState.startDate !== currentState.startDate ||
      newState.endDate !== currentState.endDate;

    if (!hasChanges) {
      console.log('Filter state unchanged, skipping update');
      return;
    }

    // Log state changes for debugging
    console.log('Filter state updated:', newState);

    // Update both subjects to ensure UI is refreshed
    this.filterStateSubject.next({ ...newState });
    this.updateActiveFilters(newState);
  }

  /**
   * Get the current filter state
   */
  getCurrentFilterState(): FilterState {
    return this.filterStateSubject.value;
  }
  /**
   * Clear the filter state to defaults
   */
  resetFilterState(): void {
    console.log('Filter state reset to defaults');

    // Create a new default state object to ensure reference changes
    const freshDefaultState = {
      searchFilter: '',
      statusFilter: 'all',
      startDate: null,
      endDate: null,
    };

    // Update both subjects with the fresh default state
    this.filterStateSubject.next(freshDefaultState);
    this.updateActiveFilters(freshDefaultState);

    // Ensure active filters are reset
    const emptyActiveFilters: ActiveFilters = {
      hasSearch: false,
      hasStatus: false,
      hasDates: false,
      searchDisplay: '',
      statusDisplay: 'All',
      dateDisplay: '',
    };
    this.activeFiltersSubject.next(emptyActiveFilters);
  }

  /**
   * Check if any filters are currently active
   */
  hasActiveFilters(): boolean {
    const state = this.filterStateSubject.value;
    return (
      !!state.searchFilter ||
      state.statusFilter !== 'all' ||
      !!state.startDate ||
      !!state.endDate
    );
  }
  /**
   * Update the active filters display information
   */
  private updateActiveFilters(state: FilterState): void {
    // Make strict checks for empty values
    const isSearchActive =
      state.searchFilter != null && state.searchFilter.trim() !== '';

    const isStatusActive =
      state.statusFilter != null &&
      state.statusFilter !== '' &&
      state.statusFilter !== 'all';

    const isDatesActive = state.startDate != null || state.endDate != null;

    const activeFilters: ActiveFilters = {
      hasSearch: isSearchActive,
      hasStatus: isStatusActive,
      hasDates: isDatesActive,
      searchDisplay: state.searchFilter || '',
      statusDisplay: this.formatStatusForDisplay(state.statusFilter),
      dateDisplay: this.formatDatesForDisplay(state.startDate, state.endDate),
    };

    console.log('Active filters updated:', activeFilters);

    // Create a new object to ensure reference changes
    this.activeFiltersSubject.next({ ...activeFilters });
  }

  /**
   * Format status value for display
   */
  private formatStatusForDisplay(status: string): string {
    if (!status || status === 'all') return 'All';

    // Capitalize first letter
    return status.charAt(0).toUpperCase() + status.slice(1);
  }

  /**
   * Format date range for display
   */
  private formatDatesForDisplay(
    startDate: Date | null,
    endDate: Date | null
  ): string {
    if (!startDate && !endDate) return '';

    const formatDate = (date: Date | null): string => {
      if (!date) return '';
      return date.toLocaleDateString();
    };

    if (startDate && endDate) {
      return `${formatDate(startDate)} - ${formatDate(endDate)}`;
    } else if (startDate) {
      return `From ${formatDate(startDate)}`;
    } else if (endDate) {
      return `Until ${formatDate(endDate)}`;
    }

    return '';
  }
}
