﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using MediatR;
using Microsoft.AspNetCore.Mvc;
using RIB.PRMetrics.Application.Dto;
using RIB.PRMetrics.Application.UseCases.Commons.Bases;
using System.ComponentModel.DataAnnotations;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetExcelReport
{
    /// <summary>
    /// Query to retrieve an exported Excel report by its UUID.
    /// </summary>
    public class GetExcelReportQuery : IRequest<BaseReponseGeneric<ExportExcelReportDto>>
    {
        /// <summary>
        /// Gets or sets the UUID of the Excel export report to retrieve.
        /// This value is required and is passed as a query parameter.
        /// </summary>
        [Required(ErrorMessage = "Uuid is required.")]
        [FromQuery(Name = "uuid")]
        public string Uuid { get; set; }
    }
}
