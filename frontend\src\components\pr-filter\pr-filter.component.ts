import {
  Component,
  EventEmitter,
  Input,
  Output,
  OnInit,
  OnDestroy,
  ViewChild,
  ElementRef,
  AfterViewInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatChipsModule } from '@angular/material/chips';
import { Subject, debounceTime, distinctUntilChanged, takeUntil } from 'rxjs';
import { FilterStateService } from '../../services/filter-state.service';

/**
 * Reusable filter component for PR data tables
 * Extracted from pr-data.component for improved reusability
 */
@Component({
  selector: 'app-pr-filter',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatButtonModule,
    MatSelectModule,
    MatTooltipModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatChipsModule,
  ],
  templateUrl: './pr-filter.component.html',
  styleUrl: './pr-filter.component.scss',
})
export class PrFilterComponent implements OnInit, OnDestroy, AfterViewInit {
  // Observable for search input changes
  private searchTerms = new Subject<string>();
  // Subject for handling component destruction
  private destroy$ = new Subject<void>();

  // Date range filter properties
  startDate: Date | null = null;
  endDate: Date | null = null;
  // Store the search filter value before view init - public for template binding
  public _searchFilterValue: string = '';

  // Reference to the search input element
  @ViewChild('searchInput') searchInputRef!: ElementRef<HTMLInputElement>;

  // Configurable debounce time in milliseconds
  @Input() debounceTime: number = 300;

  /**
   * List of status options for the status filter dropdown
   */
  @Input() statusOptions: Array<{ value: string; label: string }> = [];

  /**
   * Current value of the status filter
   */
  @Input() statusFilter: string = 'all';

  /**
   * Current value of the search filter
   */
  @Input() set searchFilter(value: string) {
    // Store the value for when the view initializes
    this._searchFilterValue = value || '';

    // Only update the DOM element if it's available
    if (this.searchInputRef?.nativeElement) {
      this.searchInputRef.nativeElement.value = value || '';
    }
  }

  // Getter for search filter to use in template binding
  get searchFilter(): string {
    return this._searchFilterValue;
  }

  /**
   * Placeholder text for search input field
   */
  @Input() searchPlaceholder: string = 'Enter PR ID';
  /**
   * Event emitted when status filter selection changes
   */
  @Output() statusFilterChange = new EventEmitter<string>();

  /**
   * Event emitted when search text changes
   */
  @Output() filterChange = new EventEmitter<Event>();

  /**
   * Event emitted when search is cleared
   */
  @Output() searchClear = new EventEmitter<HTMLInputElement>();

  /**
   * Event emitted when date range changes
   */ @Output() dateRangeChange = new EventEmitter<{
    startDate: Date | null;
    endDate: Date | null;
  }>();

  constructor(private filterStateService: FilterStateService) {}

  /**
   * Handle status filter selection change
   */
  onStatusFilterChange(): void {
    // Update filter state service directly
    this.filterStateService.updateFilterState({
      statusFilter: this.statusFilter,
    });

    // Emit to parent component
    this.statusFilterChange.emit(this.statusFilter);

    console.log('Status filter changed to:', this.statusFilter);
  }

  /**
   * Public getter for active filters observable to use in the template
   */
  get activeFilters$() {
    return this.filterStateService.activeFilters$;
  }

  /**
   * Initialize the component
   */
  ngOnInit(): void {
    // Setup debounced search
    this.searchTerms
      .pipe(
        takeUntil(this.destroy$),
        debounceTime(this.debounceTime),
        distinctUntilChanged()
      )
      .subscribe((term) => {
        // Create a synthetic event to emit
        const event = new CustomEvent('search', { detail: term });
        this.filterChange.emit(event as unknown as Event);
      });

    // Subscribe to filter state changes
    this.filterStateService.filterState$
      .pipe(takeUntil(this.destroy$))
      .subscribe((state) => {
        // Apply date filters
        if (state.startDate !== this.startDate) {
          this.startDate = state.startDate;
        }
        if (state.endDate !== this.endDate) {
          this.endDate = state.endDate;
        }
        // Apply status filter
        if (state.statusFilter !== this.statusFilter) {
          this.statusFilter = state.statusFilter;
        }

        // Apply search filter - update the bound input field value
        if (state.searchFilter !== this._searchFilterValue) {
          this._searchFilterValue = state.searchFilter || '';
          console.log(
            'Updated search input from filter state:',
            this._searchFilterValue
          );
        }
      });
  }
  /**
   * After view init - update search input with saved search filter
   */
  ngAfterViewInit(): void {
    // Use either the input property value or the filter state
    const searchValue =
      this._searchFilterValue ||
      this.filterStateService.getCurrentFilterState().searchFilter;

    if (searchValue && this.searchInputRef?.nativeElement) {
      // Set the actual DOM input element value
      this.searchInputRef.nativeElement.value = searchValue;

      // Also store it in our tracking property
      this._searchFilterValue = searchValue;

      // Make sure the filter state service is updated
      this.filterStateService.updateFilterState({ searchFilter: searchValue });

      console.log('Search input initialized with:', searchValue);
    }
  }

  /**
   * Clean up subscriptions when component is destroyed
   */
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
  /**
   * Handle search input change with debounce
   * @param term The new search term from ngModelChange
   */ onFilterChange(event: any): void {
    // Extract the search term from event or use it directly if it's a string
    let term: string;
    if (typeof event === 'string') {
      term = event.trim();
    } else if (event instanceof Event && 'target' in event) {
      term = ((event.target as HTMLInputElement).value || '').trim();
    } else if (typeof event === 'object' && event && 'detail' in event) {
      term = (event.detail || '').toString().trim();
    } else {
      term = '';
    }

    console.log('Search filter changed to:', term);

    // Update filter state service directly
    this.filterStateService.updateFilterState({ searchFilter: term });

    // Feed the term to debounced search subject
    this.searchTerms.next(term); // Create a proper CustomEvent for better compatibility
    const customEvent = new CustomEvent('search', {
      detail: term,
      bubbles: true,
      cancelable: true,
    }) as unknown as Event;

    // Add a console log to help debug the event
    console.debug('Emitting filterChange event with search term:', term);
    this.filterChange.emit(customEvent);
  }
  /**
   * Handle clearing of the search input
   * @param input The input element reference
   */ onSearchClear(input: HTMLInputElement): void {
    // Clear the input value if the input element exists
    if (input) {
      input.value = '';
    }

    // Clear our tracking property
    this._searchFilterValue = '';

    // Update filter state
    this.filterStateService.updateFilterState({ searchFilter: '' });

    // Feed empty term to debounced search subject
    this.searchTerms.next('');

    // Create a proper CustomEvent for consistency
    const customEvent = new CustomEvent('search', {
      detail: '',
      bubbles: true,
      cancelable: true,
    }) as unknown as Event;

    console.debug('Emitting empty search from onSearchClear');
    this.filterChange.emit(customEvent);

    // Emit to parent component for backward compatibility
    if (input) {
      this.searchClear.emit(input);
    } else {
      // Create a dummy input element if the original is null
      console.warn('Input element was null in onSearchClear');
      const dummyInput = document.createElement('input');
      this.searchClear.emit(dummyInput as HTMLInputElement);
    }

    console.log('Search filter cleared');
  }
  /**
   * Handle date range changes and emit event to parent component
   * Emits when either start or end date is selected or changed
   */
  onDateRangeChange(): void {
    console.log(
      'Date range changed in filter component:',
      this.startDate ? this.startDate.toISOString() : 'null',
      this.endDate ? this.endDate.toISOString() : 'null'
    );

    // Emit regardless of whether one or both dates are set
    // This ensures filtering works even with partial date ranges
    this.dateRangeChange.emit({
      startDate: this.startDate,
      endDate: this.endDate,
    });
  }

  /**
   * Clear the date filter and emit event to notify parent component
   */ clearDateFilter(): void {
    // Reset date values
    this.startDate = null;
    this.endDate = null;

    // Update filter state service
    this.filterStateService.updateFilterState({
      startDate: null,
      endDate: null,
    });

    // Emit event with null dates to clear the filter
    this.dateRangeChange.emit({
      startDate: null,
      endDate: null,
    });

    console.log('Date filters cleared');

    // Emit statusFilterChange to trigger data reload in the parent component
    this.statusFilterChange.emit(this.statusFilter);
  }
  /**
   * Reset status filter to 'all'
   */
  resetStatusFilter(): void {
    this.statusFilter = 'all';

    // Update filter state service
    this.filterStateService.updateFilterState({ statusFilter: 'all' });

    // Emit event to parent component
    this.statusFilterChange.emit(this.statusFilter);

    console.log('Status filter reset to "all"');
  }
  /**
   * Clear all filters at once
   */
  clearAllFilters(): void {
    console.log('Clearing all filters');

    // Reset the entire filter state at once
    this.filterStateService.resetFilterState();

    // Update component properties
    this.statusFilter = 'all';
    this.startDate = null;
    this.endDate = null;
    this._searchFilterValue = '';

    // Clear search input field if it exists
    if (this.searchInputRef?.nativeElement) {
      this.searchInputRef.nativeElement.value = '';
    }

    // Create a custom event for search clearing
    const searchEvent = new CustomEvent('search', {
      detail: '',
      bubbles: true,
      cancelable: true,
    }) as unknown as Event;

    // Emit events to parent components
    this.filterChange.emit(searchEvent);
    this.statusFilterChange.emit(this.statusFilter);
    this.dateRangeChange.emit({ startDate: null, endDate: null });

    // If search input exists, emit searchClear event
    if (this.searchInputRef?.nativeElement) {
      this.searchClear.emit(this.searchInputRef.nativeElement);
    }

    // Feed empty term to debounced search subject
    this.searchTerms.next('');

    console.log('All filters have been reset');
  }
}
