﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using FluentValidation;
using RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetExcelReport;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetExcelReportProgress
{
    /// <summary>
    /// Validator for <see cref="GetExcelReportProgressQuery"/> to ensure required fields are valid.
    /// </summary>
    public class GetExcelReportProgressValidator : AbstractValidator<GetExcelReportProgressQuery>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="GetExcelReportProgressValidator"/> class,
        /// defining validation rules for the query.
        /// </summary>
        public GetExcelReportProgressValidator()
        {
            RuleFor(x => x.Uuid)
                .NotEmpty().WithMessage("Uuid is required.")
                .NotNull().WithMessage("Uuid must not be null.");
        }
    }
}
