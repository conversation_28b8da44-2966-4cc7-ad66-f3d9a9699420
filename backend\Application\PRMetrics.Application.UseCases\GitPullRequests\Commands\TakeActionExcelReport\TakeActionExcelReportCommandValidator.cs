﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using FluentValidation;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Commands.TakeActionExcelReport
{
    /// <summary>
    /// Validator for <see cref="TakeActionExcelReportCommand"/> to ensure required fields are present.
    /// </summary>
    public class TakeActionExcelReportCommandValidator : AbstractValidator<TakeActionExcelReportCommand>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="TakeActionExcelReportCommandValidator"/> class,
        /// defining validation rules for the command.
        /// </summary>
        public TakeActionExcelReportCommandValidator()
        {
            // Validate that Uuid is not empty or null
            RuleFor(x => x.Uuid)
                .NotEmpty()
                .WithMessage("Uuid is required.");

            // Validate that JobStatus is not empty or null
            RuleFor(x => x.JobStatus)
                .NotEmpty()
                .WithMessage("JobStatus is required.");
        }
    }
}
