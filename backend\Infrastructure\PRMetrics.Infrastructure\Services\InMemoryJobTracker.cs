﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using RIB.PRMetrics.Application.Interface.Persistence;
using RIB.PRMetrics.Domain.Entities.JobStatus;
using RIB.PRMetrics.Domain.Entities;
using System.Collections.Concurrent;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Caching.Memory;
using RIB.PRMetrics.Infrastructure.SignalR;

namespace RIB.PRMetrics.Infrastructure.Services
{
    /// <summary>
    /// In-memory implementation of the <see cref="IJobTracker"/> interface that manages tracking and controlling export jobs.
    /// It supports adding jobs, retrieving jobs by UUID, and controlling job states such as pause, resume, and cancel.
    /// It also integrates with an in-memory cache and SignalR hub to broadcast job progress updates.
    /// </summary>
    public class InMemoryJobTracker : IJobTracker
    {
        private readonly IMemoryCache _cache;
        private readonly IHubContext<ProgressHub> _hub;

        /// <summary>
        /// Initializes a new instance of the <see cref="InMemoryJobTracker"/> class.
        /// </summary>
        /// <param name="cache">The memory cache for storing job progress.</param>
        /// <param name="hub">The SignalR hub context used to notify clients about job progress changes.</param>
        public InMemoryJobTracker(IMemoryCache cache,
            IHubContext<ProgressHub> hub)
        {
            _cache = cache;
            _hub = hub;
        }

        private readonly ConcurrentDictionary<Guid, ExportJob> _jobs = new();

        /// <summary>
        /// Adds or updates a tracked <see cref="ExportJob"/> identified by its UUID.
        /// </summary>
        /// <param name="job">The job to add or update.</param>
        public void Add(ExportJob job) => _jobs[job.Uuid] = job;

        /// <summary>
        /// Retrieves a tracked <see cref="ExportJob"/> by its UUID.
        /// </summary>
        /// <param name="uuid">The unique identifier of the job.</param>
        /// <returns>The job if found; otherwise, null.</returns>
        public ExportJob Get(Guid uuid) => _jobs.TryGetValue(uuid, out var job) ? job : null;

        /// <summary>
        /// Pauses the job execution by UUID if it is currently running.
        /// Updates job status and signals any waiting threads to pause.
        /// Optionally updates cached progress and notifies clients via SignalR.
        /// </summary>
        /// <param name="uuid">The unique identifier of the job to pause.</param>
        /// <param name="process">Optional current process progress to cache and broadcast.</param>
        public void Pause(Guid uuid, ProcessProgress process = null)
        {
            if (_jobs.TryGetValue(uuid, out var job))
            {
                if (JobStatus.Running == job.Status)
                {
                    job.Status = JobStatus.Paused;
                    job.PauseHandle.Reset(); // Pause the job thread(s)
                    if (process != null)
                    {
                        _cache.Set(job.Uuid.ToString(), process);
                        _hub.Clients.All.SendAsync("ReceiveProgress", process);
                    }
                }
            }
        }

        /// <summary>
        /// Resumes a paused job by UUID.
        /// Updates job status and signals any paused threads to continue.
        /// Optionally updates cached progress and notifies clients via SignalR.
        /// </summary>
        /// <param name="uuid">The unique identifier of the job to resume.</param>
        /// <param name="process">Optional current process progress to cache and broadcast.</param>
        public void Resume(Guid uuid, ProcessProgress process = null)
        {
            if (_jobs.TryGetValue(uuid, out var job))
            {
                if (JobStatus.Paused == job.Status)
                {
                    job.Status = JobStatus.Running;
                    job.PauseHandle.Set(); // Resume the job thread(s)
                    if (process != null)
                    {
                        _cache.Set(job.Uuid.ToString(), process);
                        _hub.Clients.All.SendAsync("ReceiveProgress", process);
                    }
                }
            }
        }

        /// <summary>
        /// Cancels a running or paused job by UUID.
        /// Updates job status and cancels the job's cancellation token.
        /// Removes progress from cache and notifies clients via SignalR.
        /// </summary>
        /// <param name="uuid">The unique identifier of the job to cancel.</param>
        /// <param name="process">Optional current process progress to broadcast after cancellation.</param>
        public void Cancel(Guid uuid, ProcessProgress process = null)
        {
            if (_jobs.TryGetValue(uuid, out var job))
            {
                job.Status = JobStatus.Cancelled;
                job.TokenSource.Cancel();
                if (process != null)
                {
                    _cache.Remove(job.Uuid);
                    process.FileUrl = string.Empty;
                    _hub.Clients.All.SendAsync("ReceiveProgress", process);
                }
            }
        }
    }
}
