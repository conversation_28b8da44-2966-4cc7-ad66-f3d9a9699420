﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using Microsoft.AspNetCore.Mvc;
using MediatR;
using RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetPullRequests;
using RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetGitPullRequestById;
using RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetGitPullRequestStatusCount;
using RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetGitPullRequestConflicts;
using RIB.PRMetrics.Application.UseCases.GitPullRequests.Commands.ExcelReportExport;
using RIB.PRMetrics.Application.UseCases.GitPullRequests.Commands.DeleteExcelReport;
using RIB.PRMetrics.Application.UseCases.GitPullRequests.Commands.TakeActionExcelReport;
using RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetExcelReport;
using RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetGitPullRequestInsights;

namespace RIB.PRMetrics.WebApi.Controllers
{
    /// <summary>
    /// Controller responsible for handling Git pull request-related API requests.
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class GitPullRequestsController : ControllerBase
    {
        private readonly IMediator _mediator;

        /// <summary>
        /// Initializes a new instance of the <see cref="GitPullRequestsController"/> class.
        /// </summary>
        /// <param name="mediator">The mediator used to send queries to the application layer.</param>
        public GitPullRequestsController(IMediator mediator)
        {
            _mediator = mediator ?? throw new ArgumentNullException(nameof(mediator));
        }

        /// <summary>
        /// Handles GET requests to retrieve Git pull requests based on the provided query.
        /// </summary>
        /// <param name="query">The query object containing parameters for retrieving Git pull requests.</param>
        /// <returns>A response containing the list of Git pull requests, or a BadRequest if unsuccessful.</returns>
        [HttpGet("GetGitPullRequest")]
        public async Task<IActionResult> GetGitPullRequest([FromQuery] GetGitPullRequestQuery query)
        {
            // Send the query to the mediator to handle the request and retrieve the data.
            var response = await _mediator.Send(query);

            // Return a successful response if the query was successful, otherwise return a BadRequest.
            if (response.Succcess)
            {
                return Ok(response);
            }

            return BadRequest(response);
        }

        /// <summary>
        /// Handles GET requests to retrieve a specific Git pull request by its ID.
        /// </summary>
        /// <param name="query">The query object containing the ID of the Git pull request to retrieve.</param>
        /// <returns>A response containing the Git pull request details, or a BadRequest if unsuccessful.</returns>
        [HttpGet("GetGitPullRequestById")]
        public async Task<IActionResult> GetGitPullRequestById([FromQuery] GetGitPullRequestByIdQuery query)
        {
            // Send the query to the mediator to handle the request and retrieve the data.
            var response = await _mediator.Send(query);

            // Return a successful response if the query was successful, otherwise return a BadRequest.
            if (response.Succcess)
            {
                return Ok(response);
            }

            return BadRequest(response);
        }

        /// <summary>
        /// Handles GET requests to retrieve Git pull request conflicts based on the provided query.
        /// </summary>
        /// <param name="query">The query object containing parameters for retrieving Git pull request conflicts.</param>
        /// <returns>A response containing the list of Git pull request conflicts, or a BadRequest if unsuccessful.</returns>
        [HttpGet("GetGitPullRequestConflicts")]
        public async Task<IActionResult> GetGitPullRequestConflicts([FromQuery] GetGitPullRequestConflictsQuery query)
        {
            // Send the query to the mediator to handle the request and retrieve the data.
            var response = await _mediator.Send(query);

            // Return a successful response if the query was successful, otherwise return a BadRequest.
            if (response.Succcess)
            {
                return Ok(response);
            }

            return BadRequest(response);
        }


        /// <summary>
        /// Retrieves the count of Git pull requests for a specified status (e.g., active, completed, abandoned)
        /// from Azure DevOps based on the provided query parameters.
        /// </summary>
        /// <param name="query">The query parameters including project name, repository name, status, etc.</param>
        /// <returns>
        /// Returns an <see cref="IActionResult"/> containing the status count if successful; otherwise, a BadRequest result.
        /// </returns>
        [HttpGet("GetGitPullRequestStatusCount")]
        public async Task<IActionResult> GetGitPullRequestStatusCountAsync([FromQuery] GetGitPullRequestStatusCountQuery query)
        {
            // Send the query to the mediator to handle the business logic and return the response.
            var response = await _mediator.Send(query);

            // If the query was successful, return an HTTP 200 OK with the result.
            if (response.Succcess)
            {
                return Ok(response);
            }

            // If the query failed, return an HTTP 400 Bad Request with the response object.
            return BadRequest(response);
        }

        /// <summary>
        /// Initiates the export of an Excel report based on the provided export command.
        /// </summary>
        /// <param name="command">The command containing export parameters.</param>
        /// <returns>
        /// Returns HTTP 200 OK with the export response if successful, or
        /// HTTP 400 Bad Request if the export fails.
        /// </returns>
        [HttpGet("ExcelReport")]
        public async Task<IActionResult> ExcelReportExport([FromQuery] ExcelReportExportCommand excelReportExportCommand)
        {
            // Send the query to the mediator to handle the business logic and return the response.
            var response = await _mediator.Send(excelReportExportCommand);

            // If the query was successful, return an HTTP 200 OK with the result.
            if (response.Succcess)
            {
                return Ok(response);
            }

            // If the query failed, return an HTTP 400 Bad Request with the response object.
            return BadRequest(response);
        }

        /// <summary>
        /// Deletes an existing Excel report export identified by the command parameters.
        /// </summary>
        /// <param name="command">The command containing the UUID of the report to delete.</param>
        /// <returns>
        /// Returns HTTP 200 OK with the deletion response if successful, or
        /// HTTP 400 Bad Request if the deletion fails.
        /// </returns>
        [HttpDelete("DeleteExcelReportExport")]
        public async Task<IActionResult> DeleteExcelReportExport([FromQuery] DeleteExcelReportCommand deleteExcelReportExportCommand)
        {
            // Send the query to the mediator to handle the business logic and return the response.
            var response = await _mediator.Send(deleteExcelReportExportCommand);

            // If the query was successful, return an HTTP 200 OK with the result.
            if (response.Succcess)
            {
                return Ok(response);
            }

            // If the query failed, return an HTTP 400 Bad Request with the response object.
            return BadRequest(response);
        }

        /// <summary>
        /// Takes an action on an Excel report export such as pause, resume, or cancel.
        /// </summary>
        /// <param name="command">The command specifying the action and the target report UUID.</param>
        /// <returns>
        /// Returns HTTP 200 OK with the updated status if successful, or
        /// HTTP 400 Bad Request if the action fails.
        /// </returns>
        [HttpPatch("TakeActionExcelReportExport")]
        public async Task<IActionResult> TakeActionExcelReportExport([FromBody] TakeActionExcelReportCommand takeActionExcelReportCommand)
        {
            // Send the query to the mediator to handle the business logic and return the response.
            var response = await _mediator.Send(takeActionExcelReportCommand);

            // If the query was successful, return an HTTP 200 OK with the result.
            if (response.Succcess)
            {
                return Ok(response);
            }

            // If the query failed, return an HTTP 400 Bad Request with the response object.
            return BadRequest(response);
        }

        /// <summary>
        /// Downloads the generated Excel report file.
        /// </summary>
        /// <param name="query">The query containing the UUID of the report to download.</param>
        /// <returns>
        /// Returns the file content with appropriate content type if the report is ready,
        /// or HTTP 400 Bad Request if the report is not ready or UUID is invalid.
        /// </returns>
        [HttpGet("ExcelReportDownload")]
        public async Task<IActionResult> ExcelReportDownloadAsync([FromQuery] GetExcelReportQuery query)
        {
            // Send the query to the mediator to handle the business logic and return the response.
            var response = await _mediator.Send(query);

            // If the query was successful, return an HTTP 200 OK with the result.
            if (response.Succcess)
            {
                return   File(response.Data.FileContent,
                    response.Data.ContentType,
                    response.Data.FileName);
            }

            // If the query failed, return an HTTP 400 Bad Request with the response object.
            return BadRequest(response);
        }

        /// <summary>
        /// Retrieves the progress status of one or more Excel report exports.
        /// </summary>
        /// <param name="query">The query containing UUIDs of the reports to check progress.</param>
        /// <returns>
        /// Returns HTTP 200 OK with the progress information if found,
        /// or HTTP 400 Bad Request if the request is invalid.
        /// </returns>
        [HttpGet("GetExcelReportProgress")]
        public async Task<IActionResult> GetExcelReportProgress([FromQuery] GetExcelReportProgressQuery getExcelReportProgress)
        {
            // Send the query to the mediator to handle the business logic and return the response.
            var response = await _mediator.Send(getExcelReportProgress);

            // If the query was successful, return an HTTP 200 OK with the result.
            if (response.Succcess)
            {
                return Ok(response);
            }

            // If the query failed, return an HTTP 400 Bad Request with the response object.
            return BadRequest(response);
        }

        [HttpGet("GetGitPullRequestInsights")]
        public async Task<IActionResult> GetGitPullRequestInsights([FromQuery] GetGitPullRequestInsightsQuery getGitPullRequestInsightsQuery)
        {
            // Send the query to the mediator to handle the business logic and return the response.
            var response = await _mediator.Send(getGitPullRequestInsightsQuery);

            // If the query was successful, return an HTTP 200 OK with the result.
            if (response.Succcess)
            {
                return Ok(response);
            }

            // If the query failed, return an HTTP 400 Bad Request with the response object.
            return BadRequest(response);
        }

    }
}
