.not-found-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100vh - 64px);  /* Adjust based on your header height */
  padding: 20px;
  background-color: #f9f9f9;
  background-image: 
    linear-gradient(45deg, #f5f5f5 25%, transparent 25%, transparent 75%, #f5f5f5 75%, #f5f5f5),
    linear-gradient(45deg, #f5f5f5 25%, transparent 25%, transparent 75%, #f5f5f5 75%, #f5f5f5);
  background-size: 20px 20px;
  background-position: 0 0, 10px 10px;
}

.not-found-card {
  max-width: 500px;
  width: 100%;
  text-align: center;
  padding: 40px 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  border-radius: 12px;
  background-color: white;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(to right, #f44336, #ff9800);
  }
}

.not-found-icon {
  font-size: 72px;
  height: 72px;
  width: 72px;
  margin-bottom: 20px;
  color: #f44336; /* Material red */
  transform: scale(2);
  margin: 30px auto;
  display: block;
  animation: pulse 1.5s infinite alternate;
}

@keyframes pulse {
  from { transform: scale(1.8); opacity: 1; }
  to { transform: scale(2.2); opacity: 0.8; }
}

mat-card-title {
  font-size: 28px;
  margin: 24px 0;
  color: #333;
  font-weight: 500;
}

mat-card-content p {
  font-size: 18px;
  color: #666;
  margin-bottom: 30px;
  line-height: 1.5;
}

mat-card-actions {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  
  button {
    padding: 8px 24px;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    mat-icon {
      margin-right: 8px;
    }
  }
}

@media (max-width: 599px) {
  .not-found-container {
    height: calc(100vh - 56px); /* Adjust for smaller mobile header */
  }
  
  .not-found-card {
    padding: 30px 20px;
    margin: 0 15px;
  }
  
  .not-found-icon {
    font-size: 54px;
    height: 54px;
    width: 54px;
    transform: scale(1.5);
  }
  
  mat-card-title {
    font-size: 22px;
  }
  
  mat-card-content p {
    font-size: 16px;
  }
}
