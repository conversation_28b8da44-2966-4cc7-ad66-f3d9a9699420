﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using MediatR;
using RIB.PRMetrics.Application.UseCases.Commons.Bases;
using RIB.PRMetrics.Domain.Entities;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Commands.ExcelReportExport
{
    /// <summary>
    /// Command to trigger the export of a Git Pull Request report to Excel.
    /// Inherits search criteria from <see cref="PullRequestSearchCriteria"/>.
    /// Returns a response wrapping the generated report's unique identifier (GUID).
    /// </summary>
    public class ExcelReportExportCommand : PullRequestSearchCriteria, IRequest<BaseReponseGeneric<Guid>>
    {
    }
}
