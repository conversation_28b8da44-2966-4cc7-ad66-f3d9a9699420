﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using MediatR;
using Microsoft.Extensions.Caching.Memory;
using RIB.PRMetrics.Application.Interface.Persistence;
using RIB.PRMetrics.Application.UseCases.Commons.Bases;
using RIB.PRMetrics.Domain.Entities;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Commands.ExcelReportExport
{
    /// <summary>
    /// Handles the <see cref="ExcelReportExportCommand"/> to initiate
    /// an Excel export process for Git Pull Requests.
    /// </summary>
    public class ExcelReportExportCommandHandler : IRequestHandler<ExcelReportExportCommand, BaseReponseGeneric<Guid>>
    {
        private readonly IJobQueue _jobQueue;
        private readonly IMemoryCache _cache;

        /// <summary>
        /// Initializes a new instance of the <see cref="ExcelReportExportCommandHandler"/> class.
        /// </summary>
        /// <param name="jobQueue">The job queue used to enqueue export jobs.</param>
        /// <param name="cache">The memory cache used to store process progress.</param>
        public ExcelReportExportCommandHandler(IJobQueue jobQueue, IMemoryCache cache)
        {
            _jobQueue = jobQueue;
            _cache = cache;
        }

        /// <summary>
        /// Handles the command by creating a new export job, initializing progress tracking,
        /// and enqueueing the job for processing.
        /// </summary>
        /// <param name="request">The export command containing search criteria.</param>
        /// <param name="cancellationToken">A token to observe cancellation requests.</param>
        /// <returns>A response containing the unique identifier of the export job.</returns>
        public Task<BaseReponseGeneric<Guid>> Handle(ExcelReportExportCommand request, CancellationToken cancellationToken)
        {
            var uuid = Guid.NewGuid();
            var response = new BaseReponseGeneric<Guid>()
            {
                Succcess = false // Ensuring default to false in case of error
            };
            try
            {
                string fileName = $"prmetrics-{request.Project}-{request.Repositories}-{DateTime.Now:dd-MM-yyyy-HH-mm-ss}-{uuid}.xlsx";

                // Set initial progress in cache
                var progress = new ProcessProgress
                {
                    Uuid = uuid,
                    Percentage = 0,
                    Status = "Pending",
                    FileName = fileName,
                    FileUrl = string.Empty
                };

                _cache.Set(uuid.ToString(), progress);

                // Enqueue job
                _jobQueue.Enqueue(new ExportJob
                {
                    Uuid = uuid,
                    FileName = fileName,
                    pullRequestSearchCriteria = request
                });

                response.Data = uuid;
                response.Succcess = true;
                response.Message = "Started the excel export process!";
            }
            catch (Exception ex)
            {
                response.Message = $"An error occurred while creating the excel export process: {ex.Message}";
                // You could log the exception here (e.g., _logger.LogError(ex, "Failed to create excel export job"));
            }

            return Task.FromResult(response);
        }
    }
}
