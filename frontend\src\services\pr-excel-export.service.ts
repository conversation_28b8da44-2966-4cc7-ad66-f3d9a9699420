import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ConfigService } from './config.service';

export interface ExcelExportRequest {
  project: string;
  repositories: string;
  patToken: string;
  status?: string;
  searchTerm?: string;
  startDate?: string;
  endDate?: string;
  pageNumber?: number;
  pageSize?: number;
}

export interface ExcelExportResponse {
  succcess: boolean; // Note: Backend uses 'succcess' with 3 c's
  data: string; // UUID of the export job
  message: string;
}

export interface ExportActionRequest {
  uuid: string;
  action: 'pause' | 'resume' | 'cancel';
}

export interface ExportActionResponse {
  succcess: boolean; // Note: Backend uses 'succcess' with 3 c's
  message: string;
}

@Injectable({
  providedIn: 'root'
})
export class PrExcelExportService {

  constructor(
    private http: HttpClient,
    private configService: ConfigService
  ) {}

  /**
   * Start Excel export for PR data
   */
  startExcelExport(request: ExcelExportRequest): Observable<ExcelExportResponse> {
    const url = `${this.configService.API_BASE_URL}/GitPullRequests/ExcelReport`;
    
    const params: any = {
      Project: request.project,
      Repositories: request.repositories,
      PATToken: request.patToken
    };

    // Add optional parameters if provided
    if (request.status && request.status !== 'all') {
      params.Status = request.status;
    }
    if (request.searchTerm) {
      params.SearchTerm = request.searchTerm;
    }
    if (request.startDate) {
      params.StartDate = request.startDate;
    }
    if (request.endDate) {
      params.EndDate = request.endDate;
    }
    if (request.pageNumber !== undefined) {
      params.PageNumber = request.pageNumber;
    }
    if (request.pageSize !== undefined) {
      params.PageSize = request.pageSize;
    }

    return this.http.get<ExcelExportResponse>(url, { params });
  }

  /**
   * Take action on an export job (pause, resume, cancel)
   */
  takeExportAction(request: ExportActionRequest): Observable<ExportActionResponse> {
    const url = `${this.configService.API_BASE_URL}/GitPullRequests/TakeActionExcelReportExport`;
    
    const body = {
      Uuid: request.uuid,
      Action: request.action
    };

    return this.http.patch<ExportActionResponse>(url, body);
  }

  /**
   * Delete an export job
   */
  deleteExport(uuid: string): Observable<ExportActionResponse> {
    const url = `${this.configService.API_BASE_URL}/GitPullRequests/DeleteExcelReportExport`;
    
    const params = {
      Uuid: uuid
    };

    return this.http.delete<ExportActionResponse>(url, { params });
  }

  /**
   * Download the exported file using the proper backend endpoint
   */
  downloadExportedFile(uuid: string): string {
    return `${this.configService.API_BASE_URL}/GitPullRequests/ExcelReportDownload?Uuid=${uuid}`;
  }

  /**
   * Get export progress from cache (if needed as fallback)
   */
  getExportProgress(uuid: string): Observable<any> {
    const url = `${this.configService.API_BASE_URL}/GitPullRequests/GetExcelReport`;
    
    const params = {
      Uuid: uuid
    };

    return this.http.get<any>(url, { params });
  }
}

