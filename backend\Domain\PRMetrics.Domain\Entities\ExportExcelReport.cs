﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Domain.Entities
{
    /// <summary>
    /// Represents an Excel export report including file metadata and content.
    /// </summary>
    public class ExportExcelReport
    {
        /// <summary>
        /// Gets or sets the name of the Excel file.
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// Gets or sets the binary content of the Excel file.
        /// </summary>
        public byte[] FileContent { get; set; }

        /// <summary>
        /// Gets or sets the MIME content type of the file (e.g., "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet").
        /// </summary>
        public string ContentType { get; set; }
    }
}
