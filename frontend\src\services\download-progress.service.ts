import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject, timer, Subscription } from 'rxjs';
import { SignalRService, ProcessProgress } from './signalr.service';
import { PrExcelExportService } from './pr-excel-export.service';
import { takeUntil } from 'rxjs/operators';

export interface DownloadProgress {
  id: string;
  fileName: string;
  progress: number; // 0-100
  status: 'preparing' | 'downloading' | 'paused' | 'completed' | 'cancelled' | 'error';
  startTime: Date;
  estimatedTimeRemaining?: string;
  downloadSpeed?: string;
  error?: string;
  uuid?: string; // Backend job UUID for SignalR tracking
  fileUrl?: string; // Download URL when completed
}

@Injectable({
  providedIn: 'root'
})
export class DownloadProgressService {
  private downloads = new Map<string, DownloadProgress>();
  private downloadSubjects = new Map<string, BehaviorSubject<DownloadProgress>>();
  private cancelSubjects = new Map<string, Subject<void>>();
  private signalRSubscriptions = new Map<string, Subscription>();

  constructor(
    private signalRService: SignalRService,
    private prExcelExportService: PrExcelExportService
  ) { }

  /**
   * Start a new download process
   * @param fileName Name of the file being downloaded
   * @returns Download ID and observable for progress tracking
   */
  startDownload(fileName: string): { id: string, progress$: Observable<DownloadProgress> } {
    const id = this.generateDownloadId();
    const startTime = new Date();
    const initialProgress: DownloadProgress = {
      id,
      fileName,
      progress: 0,
      status: 'preparing',
      startTime
    };

    this.downloads.set(id, initialProgress);
    const subject = new BehaviorSubject<DownloadProgress>(initialProgress);
    this.downloadSubjects.set(id, subject);
    this.cancelSubjects.set(id, new Subject<void>());

    return {
      id,
      progress$: subject.asObservable()
    };
  }

  /**
   * Start a SignalR-tracked download process
   * @param fileName Name of the file being downloaded
   * @param uuid Backend job UUID for SignalR tracking
   * @returns Download ID and observable for progress tracking
   */
  startSignalRDownload(fileName: string, uuid: string): { id: string, progress$: Observable<DownloadProgress> } {
    const id = this.generateDownloadId();
    const startTime = new Date();

    const initialProgress: DownloadProgress = {
      id,
      fileName,
      progress: 0,
      status: 'preparing',
      startTime,
      uuid
    };

    this.downloads.set(id, initialProgress);
    const subject = new BehaviorSubject<DownloadProgress>(initialProgress);
    this.downloadSubjects.set(id, subject);
    this.cancelSubjects.set(id, new Subject<void>());

    // Subscribe to SignalR updates for this job
    this.subscribeToSignalRUpdates(id, uuid);

    return {
      id,
      progress$: subject.asObservable()
    };
  }

  /**
   * Subscribe to SignalR progress updates for a specific job
   */
  private subscribeToSignalRUpdates(downloadId: string, uuid: string): void {
    const subscription = this.signalRService.getProgressForJob(uuid).subscribe(
      (progress: ProcessProgress | null) => {
        if (progress) {
          this.updateFromSignalR(downloadId, progress);
        }
      }
    );

    this.signalRSubscriptions.set(downloadId, subscription);
  }

  /**
   * Update download progress from SignalR data
   */
  private updateFromSignalR(downloadId: string, signalRProgress: ProcessProgress): void {
    const download = this.downloads.get(downloadId);
    const subject = this.downloadSubjects.get(downloadId);

    if (!download || !subject) return;

    // Map SignalR status to download status
    let status: DownloadProgress['status'] = 'downloading';
    switch (signalRProgress.jobStatus.toLowerCase()) {
      case 'pending':
        status = 'preparing';
        break;
      case 'running':
        status = 'downloading';
        break;
      case 'paused':
        status = 'paused';
        break;
      case 'completed':
        status = 'completed';
        break;
      case 'cancelled':
        status = 'cancelled';
        break;
      case 'error':
      case 'failed':
        status = 'error';
        break;
    }

    const updatedProgress: DownloadProgress = {
      ...download,
      progress: signalRProgress.percentage,
      status,
      uuid: signalRProgress.uuid,
      fileUrl: signalRProgress.fileUrl
    };

    this.downloads.set(downloadId, updatedProgress);
    subject.next(updatedProgress);

    // Clean up if completed or cancelled
    if (status === 'completed' || status === 'cancelled' || status === 'error') {
      setTimeout(() => {
        this.cleanupDownload(downloadId);
      }, status === 'completed' ? 5000 : 2000);
    }
  }

  /**
   * Simulate download progress (for Excel generation)
   * @param downloadId Download ID
   * @param totalSteps Total number of steps in the process
   */
  simulateProgress(downloadId: string, totalSteps: number = 100): Promise<void> {
    return new Promise((resolve, reject) => {
      const download = this.downloads.get(downloadId);
      const subject = this.downloadSubjects.get(downloadId);
      const cancelSubject = this.cancelSubjects.get(downloadId);

      if (!download || !subject || !cancelSubject) {
        reject('Download not found');
        return;
      }

      let currentStep = 0;
      const stepDuration = 50; // milliseconds per step

      // Update status to downloading
      this.updateDownloadStatus(downloadId, 'downloading');

      const progressTimer = timer(0, stepDuration).pipe(
        takeUntil(cancelSubject)
      ).subscribe(() => {
        const currentDownload = this.downloads.get(downloadId);
        
        if (!currentDownload || currentDownload.status === 'cancelled' || currentDownload.status === 'paused') {
          progressTimer.unsubscribe();
          if (currentDownload?.status === 'cancelled') {
            reject('Download cancelled');
          }
          return;
        }

        currentStep++;
        const progress = Math.min((currentStep / totalSteps) * 100, 100);
        
        // Calculate estimated time remaining
        const elapsed = Date.now() - currentDownload.startTime.getTime();
        const estimatedTotal = (elapsed / progress) * 100;
        const remaining = estimatedTotal - elapsed;
        const estimatedTimeRemaining = this.formatTime(remaining);

        // Calculate download speed (simulated)
        const downloadSpeed = this.calculateDownloadSpeed(elapsed, progress);

        const updatedProgress: DownloadProgress = {
          ...currentDownload,
          progress,
          estimatedTimeRemaining,
          downloadSpeed
        };

        this.downloads.set(downloadId, updatedProgress);
        subject.next(updatedProgress);

        if (progress >= 100) {
          progressTimer.unsubscribe();
          this.completeDownload(downloadId);
          resolve();
        }
      });
    });
  }

  /**
   * Pause a download
   * @param downloadId Download ID
   */
  pauseDownload(downloadId: string): void {
    const download = this.downloads.get(downloadId);
    if (download?.uuid) {
      // Use SignalR service for backend-tracked downloads
      this.prExcelExportService.takeExportAction({
        uuid: download.uuid,
        action: 'pause'
      }).subscribe({
        next: (response) => {
          if (!response.succcess) {
            console.error('Failed to pause download:', response.message);
          }
        },
        error: (error) => {
          console.error('Error pausing download:', error);
        }
      });
    } else {
      // Fallback for local downloads
      this.updateDownloadStatus(downloadId, 'paused');
    }
  }

  /**
   * Resume a paused download
   * @param downloadId Download ID
   */
  resumeDownload(downloadId: string): void {
    const download = this.downloads.get(downloadId);
    if (download?.uuid) {
      // Use SignalR service for backend-tracked downloads
      this.prExcelExportService.takeExportAction({
        uuid: download.uuid,
        action: 'resume'
      }).subscribe({
        next: (response) => {
          if (!response.succcess) {
            console.error('Failed to resume download:', response.message);
          }
        },
        error: (error) => {
          console.error('Error resuming download:', error);
        }
      });
    } else if (download && download.status === 'paused') {
      // Fallback for local downloads
      this.updateDownloadStatus(downloadId, 'downloading');
      const remainingSteps = 100 - download.progress;
      this.simulateProgress(downloadId, remainingSteps);
    }
  }

  /**
   * Cancel a download
   * @param downloadId Download ID
   */
  cancelDownload(downloadId: string): void {
    const download = this.downloads.get(downloadId);
    if (download?.uuid) {
      // Use SignalR service for backend-tracked downloads
      this.prExcelExportService.takeExportAction({
        uuid: download.uuid,
        action: 'cancel'
      }).subscribe({
        next: (response) => {
          if (!response.succcess) {
            console.error('Failed to cancel download:', response.message);
          }
        },
        error: (error) => {
          console.error('Error cancelling download:', error);
        }
      });
    } else {
      // Fallback for local downloads
      const cancelSubject = this.cancelSubjects.get(downloadId);
      if (cancelSubject) {
        cancelSubject.next();
        cancelSubject.complete();
      }

      this.updateDownloadStatus(downloadId, 'cancelled');

      // Clean up after a delay
      setTimeout(() => {
        this.cleanupDownload(downloadId);
      }, 2000);
    }
  }

  /**
   * Complete a download
   * @param downloadId Download ID
   */
  private completeDownload(downloadId: string): void {
    this.updateDownloadStatus(downloadId, 'completed');
    
    // Clean up after a delay
    setTimeout(() => {
      this.cleanupDownload(downloadId);
    }, 5000);
  }

  /**
   * Set download error
   * @param downloadId Download ID
   * @param error Error message
   */
  setDownloadError(downloadId: string, error: string): void {
    const download = this.downloads.get(downloadId);
    const subject = this.downloadSubjects.get(downloadId);
    
    if (download && subject) {
      const updatedProgress: DownloadProgress = {
        ...download,
        status: 'error',
        error
      };
      
      this.downloads.set(downloadId, updatedProgress);
      subject.next(updatedProgress);
    }
  }

  /**
   * Get download progress observable
   * @param downloadId Download ID
   */
  getDownloadProgress(downloadId: string): Observable<DownloadProgress> | null {
    const subject = this.downloadSubjects.get(downloadId);
    return subject ? subject.asObservable() : null;
  }

  /**
   * Get all active downloads
   */
  getActiveDownloads(): DownloadProgress[] {
    return Array.from(this.downloads.values()).filter(
      download => download.status !== 'completed' && download.status !== 'cancelled'
    );
  }

  /**
   * Update download status
   */
  private updateDownloadStatus(downloadId: string, status: DownloadProgress['status']): void {
    const download = this.downloads.get(downloadId);
    const subject = this.downloadSubjects.get(downloadId);
    
    if (download && subject) {
      const updatedProgress: DownloadProgress = {
        ...download,
        status
      };
      
      this.downloads.set(downloadId, updatedProgress);
      subject.next(updatedProgress);
    }
  }

  /**
   * Clean up download resources
   */
  private cleanupDownload(downloadId: string): void {
    const subject = this.downloadSubjects.get(downloadId);
    const cancelSubject = this.cancelSubjects.get(downloadId);
    const signalRSubscription = this.signalRSubscriptions.get(downloadId);

    if (subject) {
      subject.complete();
      this.downloadSubjects.delete(downloadId);
    }
    if (cancelSubject) {
      cancelSubject.complete();
      this.cancelSubjects.delete(downloadId);
    }

    if (signalRSubscription) {
      signalRSubscription.unsubscribe();
      this.signalRSubscriptions.delete(downloadId);
    }

    this.downloads.delete(downloadId);
  }

  /**
   * Generate unique download ID
   */
  private generateDownloadId(): string {
    return 'download_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Format time in milliseconds to human readable string
   */
  private formatTime(milliseconds: number): string {
    if (milliseconds <= 0) return '0s';
    
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  /**
   * Calculate simulated download speed
   */
  private calculateDownloadSpeed(elapsedMs: number, progress: number): string {
    if (elapsedMs <= 0 || progress <= 0) return '0 KB/s';
    
    // Simulate file size and calculate speed
    const simulatedFileSizeKB = 500; // Assume 500KB file
    const downloadedKB = (progress / 100) * simulatedFileSizeKB;
    const elapsedSeconds = elapsedMs / 1000;
    const speedKBps = downloadedKB / elapsedSeconds;
    
    if (speedKBps > 1024) {
      return `${(speedKBps / 1024).toFixed(1)} MB/s`;
    } else {
      return `${speedKBps.toFixed(1)} KB/s`;
    }
  }
}


