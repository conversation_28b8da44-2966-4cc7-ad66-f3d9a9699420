﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using RIB.PRMetrics.Domain.Entities;

namespace RIB.PRMetrics.Application.Interface.Persistence
{
    /// <summary>
    /// Defines the contract for a job queue that handles the enqueueing and dequeueing of export jobs.
    /// </summary>
    public interface IJobQueue
    {
        /// <summary>
        /// Adds a new export job to the queue.
        /// </summary>
        /// <param name="job">The <see cref="ExportJob"/> to enqueue.</param>
        void Enqueue(ExportJob job);

        /// <summary>
        /// Attempts to remove and return the next export job from the queue.
        /// </summary>
        /// <param name="job">When this method returns, contains the <see cref="ExportJob"/> removed from the queue, if one was available; otherwise, null.</param>
        /// <returns><c>true</c> if a job was successfully dequeued; otherwise, <c>false</c>.</returns>
        bool TryDequeue(out ExportJob job);
    }
}
