<div class="app-container">
  <main class="main-content">
    <mat-card class="data-card">
      <!-- PR header and status count -->
      <mat-card-header style="display: block">
        <app-pr-card-header
          title="Pull Request Analysis"
          subtitle="Review and analyze pull request metrics for {{
            selectedRepo
          }}"
          [totalCount]="totalCount"
          [completedCount]="completedCount"
          [abandonedCount]="abandonedCount"
          [loadingActiveCount]="loadingActiveCount"
          [loadingCompletedCount]="loadingCompletedCount"
          [loadingAbandonedCount]="loadingAbandonedCount"
          [currentStatusFilter]="statusFilter"
          (statusFilterChange)="setStatusFilterAndLoad($event)"
        >
        </app-pr-card-header> </mat-card-header
      ><mat-card-content>
        <!-- Filter and Export Section -->
        <div class="filter-export-container">
          <!-- Filter - Using the new reusable filter component -->
          <div class="filter-section">
            <app-pr-filter
              [statusOptions]="statusOptions"
              [(statusFilter)]="statusFilter"
              (statusFilterChange)="loadInitialPRs()"
              (filterChange)="applyFilter($event)"
              (searchClear)="clearSearch($event)"
              (dateRangeChange)="onDateRangeChanged($event)"
            >
            </app-pr-filter>
          </div>

          <!-- Export Section -->
          <div class="export-section">
            <button
              mat-raised-button
              color="primary"
              class="export-button"
              (click)="exportToExcel()"
              [disabled]="isExporting || dataSource.data.length === 0"
              matTooltip="Export PR table data to Excel">
              <mat-icon>file_download</mat-icon>
              <span>Export Excel</span>
            </button>
          </div>
        </div>

        <!-- Download progress UI -->
        <div class="download-progress-container" *ngIf="exportDownloadId">
          <app-download-ui
            [downloadId]="exportDownloadId"
            [showCard]="false">
          </app-download-ui>
        </div>

        <cdk-virtual-scroll-viewport
          itemSize="64"
          class="virtual-scroll-viewport"
          (scrolledIndexChange)="onScroll()"
        >
          <table mat-table [dataSource]="dataSource" matSort class="pr-table">
            <!-- Serial Number Column -->
            <ng-container matColumnDef="serialNumber">
              <th mat-header-cell *matHeaderCellDef class="serial-number-cell">
                <div class="header-cell-content">
                  <span>Sr. No</span>
                </div>
              </th>
              <td
                mat-cell
                *matCellDef="let pr; let i = index"
                class="serial-number-cell"
              >
                {{ i + 1 }}
              </td>
            </ng-container>

            <!-- PR ID Column -->
            <ng-container matColumnDef="pullRequestId">
              <th mat-header-cell *matHeaderCellDef>
                <div class="header-cell-content" mat-sort-header>
                  <span>PR ID</span>
                  <mat-icon class="sort-icon">unfold_more</mat-icon>
                </div>
              </th>
              <td
                mat-cell
                *matCellDef="let pr"
                class="id-cell clickable"
                (click)="navigateToPrDetails(pr.pullRequestId)"
                matTooltip="View PR details"
              >
                <span class="pr-id-link">#{{ pr.pullRequestId }}</span>
              </td>
            </ng-container>
            <!-- Title Column -->
            <ng-container matColumnDef="title">
              <th mat-header-cell *matHeaderCellDef>
                <div class="header-cell-content" mat-sort-header>
                  <span>Title</span>
                  <mat-icon class="sort-icon">unfold_more</mat-icon>
                </div>
              </th>
              <td
                mat-cell
                *matCellDef="let pr"
                class="title-cell"
                [matTooltip]="pr.title"
              >
                {{ pr.title }}
              </td>
            </ng-container>
            <!-- Creation Date Column -->
            <ng-container matColumnDef="creationDate">
              <th mat-header-cell *matHeaderCellDef>
                <div class="header-cell-content" mat-sort-header>
                  <span>Created On</span>
                  <mat-icon class="sort-icon">unfold_more</mat-icon>
                </div>
              </th>
              <td mat-cell *matCellDef="let pr" class="date-cell">
                <div class="date-info">
                  <span class="date">{{
                    pr.creationDate | date : 'MMM d, y'
                  }}</span>
                  <span class="time">{{
                    pr.creationDate | date : 'h:mm a'
                  }}</span>
                </div>
              </td>
            </ng-container>
            <!-- Timelapse Column -->
            <ng-container matColumnDef="timelapse">
              <th mat-header-cell *matHeaderCellDef>
                <div
                  class="header-cell-content"
                  mat-sort-header="timelapse"
                  disableClear
                >
                  <span>Timelapsed</span>
                  <mat-icon class="sort-icon">unfold_more</mat-icon>
                </div>
              </th>
              <td mat-cell *matCellDef="let pr" class="timelapse-cell">
                {{ getTimelapse(pr) }}
              </td>
            </ng-container>

            <!-- Active Comments -->
            <ng-container matColumnDef="activeComments">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                <div class="header-cell-content">
                  <span>PR Comments</span>
                  <mat-icon class="sort-icon">unfold_more</mat-icon>
                </div>
              </th>
              <td
                mat-cell
                *matCellDef="let pr"
                class="comments-cell clickable"
                (click)="navigateToPrDetails(pr.pullRequestId)"
              >
                <button
                  mat-button
                  class="comment-button"
                  [ngClass]="{
                    'has-comments': pr.activeComments > 0,
                    'has-resolved':
                      pr.resolvedComments > 0 && pr.activeComments === 0,
                    'no-comments':
                      (pr.totalComments === 0 || !pr.totalComments) &&
                      !pr.loadingComments
                  }"
                  (click)="fetchCommentCount(pr, $event)"
                >
                  <!-- [matTooltip]="pr.loadingComments ? 'Loading comments...' : 'Refresh comment count'" -->
                  <mat-icon
                    class="comments-icon"
                    [class.loading]="pr.loadingComments"
                    >{{ pr.loadingComments ? 'sync' : 'refresh' }}</mat-icon
                  >
                  <span
                    *ngIf="
                      pr.totalComments !== undefined &&
                      (pr.activeComments >= 0 || pr.resolvedComments >= 0)
                    "
                  >
                    <strong>{{ pr.activeComments }}</strong> Active /
                    <strong>{{ pr.totalComments }}</strong> Total
                  </span>
                  <span *ngIf="pr.totalComments === undefined">
                    Fetch Comments
                  </span>
                </button>
              </td>
            </ng-container>
            <!-- Status Column -->
            <ng-container matColumnDef="status">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                <div class="header-cell-content">
                  <span>Status</span>
                  <mat-icon class="sort-icon">unfold_more</mat-icon>
                </div>
              </th>
              <td mat-cell *matCellDef="let pr" class="status-cell">
                <span
                  class="status-badge"
                  [ngClass]="getStatusClass(pr.status)"
                >
                  <span class="status-dot"></span>
                  {{ pr.isDraft === 'true' ? 'Draft' : pr.status }}
                </span>
              </td>
            </ng-container>

            <!-- Created By Column -->
            <ng-container matColumnDef="createdBy">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                <div class="header-cell-content">
                  <span>Author</span>
                  <mat-icon class="sort-icon">unfold_more</mat-icon>
                </div>
              </th>
              <td mat-cell *matCellDef="let pr" class="author-cell">
                <div class="author-info">
                  <span class="author-name">{{
                    pr.createdBy.displayName
                  }}</span>
                </div>
              </td>
            </ng-container>
            <!-- reviewers Column -->
            <ng-container matColumnDef="reviewers">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                <div class="header-cell-content">
                  <span>Reviewers</span>
                  <mat-icon class="sort-icon">unfold_more</mat-icon>
                </div>
              </th>
              <td
                mat-cell
                *matCellDef="let pr"
                class="author-cell clickable"
                (click)="openApproverModal(pr.reviewers)"
              >
                <div class="author-info">
                  <div class="reviewers-preview">
                    <ng-container
                      *ngIf="pr.reviewers && pr.reviewers.length > 0"
                    >
                      <span class="author-name">{{
                        pr.reviewers[0].displayName
                      }}</span>
                      <span
                        *ngIf="pr.reviewers.length > 1"
                        class="more-reviewers"
                        >+{{ pr.reviewers.length - 1 }} more</span
                      >
                    </ng-container>
                    <span *ngIf="!pr.reviewers || pr.reviewers.length === 0"
                      >No approvers</span
                    >
                  </div>
                </div>
              </td>
            </ng-container>

            <!-- Source Branch -->
            <ng-container matColumnDef="sourceRefName">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                <div class="header-cell-content">
                  <span>Source Branch</span>
                  <mat-icon class="sort-icon">unfold_more</mat-icon>
                </div>
              </th>
              <td
                mat-cell
                *matCellDef="let pr"
                class="branch-cell"
                [matTooltip]="formatBranchName(pr.sourceRefName)"
              >
                <div class="branch-info">
                  <mat-icon class="branch-icon">call_split</mat-icon>
                  <span title="{{ formatBranchName(pr.sourceRefName) }}">{{
                    formatBranchName(pr.sourceRefName)
                  }}</span>
                </div>
              </td>
            </ng-container>

            <!-- Pipeline Status Column -->
            <ng-container matColumnDef="pipelineStatus">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                <div class="header-cell-content">
                  <span>Pipeline Status</span>
                  <mat-icon class="sort-icon">unfold_more</mat-icon>
                </div>
              </th>
              <td mat-cell *matCellDef="let pr" class="pipeline-status-cell">
                <div class="pipeline-info" *ngIf="!pr.loadingPipelineStatus">
                  <ng-container
                    *ngIf="pr.status === 'completed'; else pipelineStatusLogic"
                  >
                    <span
                      style="cursor: pointer"
                      (click)="openBuildTimeline(pr)"
                      class="pipeline-badge status-success"
                      >Completed</span
                    >
                  </ng-container>
                  <ng-template #pipelineStatusLogic>
                    <ng-container
                      *ngIf="pr.activePolicy; else singlePipelineStatus"
                    >
                      <span
                        class="pipeline-badge"
                        [ngClass]="
                          getPipelineStatusClass(pr.activePolicy.displayStatus)
                        "
                        (click)="openBuildTimeline(pr)"
                        [matTooltip]="
                          pr.activePolicy.displayText ||
                          pr.activePolicy.displayName
                        "
                        style="cursor: pointer"
                        [class.clickable]="pr.buildId"
                        [attr.title]="
                          pr.activePolicy.displayStatus === 3 &&
                          pr.activePolicy.errorMessage
                            ? pr.activePolicy.errorMessage
                            : null
                        "
                      >
                        <mat-icon class="status-dot">{{
                          getPipelineStatusIcon(pr.activePolicy.displayStatus)
                        }}</mat-icon>
                        {{
                          pr.activePolicy.displayName ||
                            'Reviewers have not approved'
                        }}
                      </span>
                    </ng-container>
                    <ng-template #singlePipelineStatus>
                      <span
                        class="pipeline-badge"
                        [ngClass]="
                          getPipelineStatusClass(pr.pipelineStatusDisplay)
                        "
                        (click)="openBuildTimeline(pr)"
                        [matTooltip]="
                          pr.buildId
                            ? 'Click to view build details'
                            : 'No build details available'
                        "
                        style="cursor: pointer"
                        [class.clickable]="pr.buildId"
                      >
                        <mat-icon class="status-dot">{{
                          getPipelineStatusIcon(pr.pipelineStatusDisplay)
                        }}</mat-icon>
                        {{ pr.pipelineStatus }}
                      </span>
                    </ng-template>
                  </ng-template>
                </div>
                <div
                  *ngIf="pr.loadingPipelineStatus"
                  class="loading-spinner-container loader-center"
                >
                  <div class="loader" style="width: 20px; height: 20px"></div>
                </div>
              </td>
            </ng-container>

            <!-- merge status column -->
            <!-- <ng-container matColumnDef="mergeStatus">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                <div class="header-cell-content">
                  <span>Merge Status</span>
                  <mat-icon class="sort-icon">unfold_more</mat-icon>
                </div>
              </th>              <td mat-cell *matCellDef="let pr" class="date-cell">
                <div class="merge-info">
                  <span class="merge" [ngClass]="{
                          'has-resolved': pr.mergeStatus === 'succeeded',
                          'has-comments': pr.mergeStatus === 'conflicts',
                        }" >{{ pr.mergeStatus }}</span>
                </div>
              </td>
            </ng-container> -->

            <!-- Target Branch -->
            <!-- <ng-container matColumnDef="targetRefName">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                <div class="header-cell-content">
                  <span>Target Branch</span>
                  <mat-icon class="sort-icon">unfold_more</mat-icon>
                </div>
              </th>
              <td mat-cell *matCellDef="let pr" class="branch-cell" [matTooltip]="formatBranchName(pr.targetRefName)">
                <div class="branch-info">
                  <mat-icon class="branch-icon">merge_type</mat-icon>
                  <span title="{{ formatBranchName(pr.targetRefName) }}">{{ formatBranchName(pr.targetRefName) }}</span>
                </div>
              </td>
            </ng-container> -->

            <tr
              mat-header-row
              *matHeaderRowDef="displayedColumns; sticky: true"
            ></tr>
            <tr
              mat-row
              *matRowDef="let row; columns: displayedColumns"
              class="pr-row"
            ></tr>
            <tr class="mat-row" *matNoDataRow>
              <td class="mat-cell no-data-cell" colspan="11">
                <div
                  class="no-data-message"
                  style="text-align: center; padding: 20px"
                >
                  <!-- Initial Loading Spinner (shown only on first load) -->
                  <div
                    *ngIf="isLoading && !dataSource.data.length"
                    class="loading-container loader-center"
                  >
                    <div class="loader"></div>
                    <p>Loading pull requests...</p>
                  </div>

                  <!-- No Results Message (shown when search returns no results) -->
                  <div
                    *ngIf="!isLoading && currentFilter"
                    class="no-results-message"
                  >
                    <mat-icon>search_off</mat-icon>
                    <p>No pull request found for your search</p>
                  </div>

                  <!-- No Data Message (shown when there's no data and no search) -->
                  <div
                    *ngIf="
                      !isLoading &&
                      !currentFilter &&
                      dataSource.data.length === 0
                    "
                    class="no-data-info"
                  >
                    <mat-icon>info_outline</mat-icon>
                    <p>{{ error }}</p>
                  </div>
                </div>
              </td>
            </tr>
          </table>
          <div *ngIf="isLoadingMore" class="loading-container">
            <mat-progress-spinner
              diameter="40"
              color="primary"
              mode="indeterminate"
            ></mat-progress-spinner>
            <p>Loading more pull requests...</p>
          </div>
          <!-- <div *ngIf="error" class="error-message">
            <div>
              <mat-icon style="font-size: 24px; margin-bottom: 10px"
                >info_outline</mat-icon
              >
            </div>
            <div>{{ error }}</div>
          </div> -->
          <!-- <div
            *ngIf="hasMore && !isLoadingMore && !error"
            class="load-more-container"
          >
            <button class="load-more" mat-raised-button color="primary" (click)="loadMorePRs()">
              Load More
            </button>
          </div> -->
        </cdk-virtual-scroll-viewport>
      </mat-card-content>
    </mat-card>
  </main>

  <!-- Footer -->
  <footer class="app-footer">
    <div class="footer-content">
      <div class="footer-left">
        <p>&copy; 2025 RIB Software SE. All rights reserved.</p>
      </div>
      <div class="footer-right">
        <div class="refresh-info">
          <mat-icon>update</mat-icon>
          <span
            >Last updated:
            {{ lastUpdated | date : 'MMM d, y, h:mm:ss a' }}</span
          >
        </div>
      </div>
    </div>
  </footer>
</div>
