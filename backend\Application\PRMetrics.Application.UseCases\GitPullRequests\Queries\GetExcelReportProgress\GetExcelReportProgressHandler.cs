﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using AutoMapper;
using MediatR;
using RIB.PRMetrics.Application.Dto;
using RIB.PRMetrics.Application.Interface.Persistence;
using RIB.PRMetrics.Application.UseCases.Commons.Bases;
using RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetExcelReport;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetExcelReportProgress
{
    /// <summary>
    /// Handles the <see cref="GetExcelReportProgressQuery"/> to retrieve the progress of Excel report exports by UUID.
    /// </summary>
    public class GetExcelReportProgressHandler : IRequestHandler<GetExcelReportProgressQuery, BaseReponseGeneric<List<ProcessProgressDto>>>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        /// <summary>
        /// Initializes a new instance of the <see cref="GetExcelReportProgressHandler"/> class.
        /// </summary>
        /// <param name="unitOfWork">Unit of work for accessing repositories.</param>
        /// <param name="mapper">AutoMapper instance for mapping entities to DTOs.</param>
        public GetExcelReportProgressHandler(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        /// <summary>
        /// Handles the query to fetch the progress information of an Excel report export job by UUID.
        /// </summary>
        /// <param name="request">The query containing the UUID of the report.</param>
        /// <param name="cancellationToken">Cancellation token.</param>
        /// <returns>A response containing a list of <see cref="ProcessProgressDto"/> with progress details.</returns>
        public async Task<BaseReponseGeneric<List<ProcessProgressDto>>> Handle(GetExcelReportProgressQuery request, CancellationToken cancellationToken)
        {
            var response = new BaseReponseGeneric<List<ProcessProgressDto>>();

            try
            {
                var excelExportReport = await _unitOfWork.GitPullRequestsRepository.GetExcelReportProgressAsync(request.Uuid);

                if (excelExportReport is not null)
                {
                    response.Data = _mapper.Map<List<ProcessProgressDto>>(excelExportReport);
                    response.Succcess = true;
                    response.Message = "Excel Report File Progress fetched successfully.";
                }
                else
                {
                    response.Succcess = false;
                    response.Message = "Excel Report File Progress not found.";
                }
            }
            catch (Exception ex)
            {
                response.Succcess = false;
                response.Message = $"Error occurred: {ex.Message}";
            }

            return response;
        }
    }
}
