import { Injectable } from '@angular/core';
import { HubConnection, HubConnectionBuilder, LogLevel } from '@microsoft/signalr';
import { BehaviorSubject, Observable } from 'rxjs';
import { ConfigService } from './config.service';

export interface ProcessProgress {
  uuid: string;
  percentage: number;
  status: string;
  fileName: string;
  jobStatus: string;
  fileUrl?: string;
}

@Injectable({
  providedIn: 'root'
})
export class SignalRService {
  private hubConnection: HubConnection | null = null;
  private connectionState = new BehaviorSubject<boolean>(false);
  private progressUpdates = new BehaviorSubject<ProcessProgress | null>(null);

  // Observable streams
  public connectionState$ = this.connectionState.asObservable();
  public progressUpdates$ = this.progressUpdates.asObservable();

  constructor(private configService: ConfigService) {}

  /**
   * Start the SignalR connection to the backend ProgressHub
   */
  public async startConnection(): Promise<void> {
    if (this.hubConnection && this.hubConnection.state === 'Connected') {
      console.log('SignalR connection already established');
      return;
    }

    try {
      // Build the connection - remove /api from the URL for SignalR
      const signalRUrl = this.configService.API_BASE_URL.replace('/api', '') + '/progressHub';
      console.log('Connecting to SignalR hub at:', signalRUrl);

      this.hubConnection = new HubConnectionBuilder()
        .withUrl(signalRUrl)
        .withAutomaticReconnect()
        .configureLogging(LogLevel.Information)
        .build();

      // Set up event handlers
      this.setupEventHandlers();

      // Start the connection
      await this.hubConnection.start();
      console.log('SignalR connection established successfully');
      this.connectionState.next(true);

    } catch (error) {
      console.error('Error starting SignalR connection:', error);
      this.connectionState.next(false);
      throw error;
    }
  }

  /**
   * Stop the SignalR connection
   */
  public async stopConnection(): Promise<void> {
    if (this.hubConnection) {
      try {
        await this.hubConnection.stop();
        console.log('SignalR connection stopped');
        this.connectionState.next(false);
      } catch (error) {
        console.error('Error stopping SignalR connection:', error);
      }
    }
  }

  /**
   * Set up event handlers for SignalR messages
   */
  private setupEventHandlers(): void {
    if (!this.hubConnection) return;

    // Listen for progress updates from the backend
    this.hubConnection.on('ReceiveProgress', (progress: ProcessProgress) => {
      console.log('Received progress update:', progress);
      this.progressUpdates.next(progress);
    });

    // Handle connection events
    this.hubConnection.onclose((error) => {
      console.log('SignalR connection closed:', error);
      this.connectionState.next(false);
    });

    this.hubConnection.onreconnecting((error) => {
      console.log('SignalR reconnecting:', error);
      this.connectionState.next(false);
    });

    this.hubConnection.onreconnected((connectionId) => {
      console.log('SignalR reconnected:', connectionId);
      this.connectionState.next(true);
    });
  }

  /**
   * Get the current connection state
   */
  public isConnected(): boolean {
    return this.hubConnection?.state === 'Connected';
  }

  /**
   * Get progress updates for a specific job UUID
   */
  public getProgressForJob(uuid: string): Observable<ProcessProgress | null> {
    return new Observable(observer => {
      const subscription = this.progressUpdates$.subscribe(progress => {
        if (progress && progress.uuid === uuid) {
          observer.next(progress);
        }
      });

      return () => subscription.unsubscribe();
    });
  }
}
