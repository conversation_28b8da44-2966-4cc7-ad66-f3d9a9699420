﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using RIB.PRMetrics.Application.Interface.Persistence;
using RIB.PRMetrics.Domain.Entities;
using System.Collections.Concurrent;

namespace RIB.PRMetrics.Infrastructure.Services
{
    /// <summary>
    /// An in-memory implementation of the <see cref="IJobQueue"/> interface using a thread-safe queue.
    /// This queue stores <see cref="ExportJob"/> instances for processing in a FIFO manner.
    /// </summary>
    public class InMemoryJobQueue : IJobQueue
    {
        // Internal thread-safe queue to hold export jobs
        private readonly ConcurrentQueue<ExportJob> _jobs = new();

        /// <summary>
        /// Adds an <see cref="ExportJob"/> to the end of the queue.
        /// </summary>
        /// <param name="job">The export job to enqueue.</param>
        public void Enqueue(ExportJob job) => _jobs.Enqueue(job);

        /// <summary>
        /// Attempts to remove and return the object at the beginning of the queue.
        /// </summary>
        /// <param name="job">When this method returns, contains the dequeued job if the operation was successful; otherwise, null.</param>
        /// <returns><c>true</c> if an item was dequeued successfully; otherwise, <c>false</c>.</returns>
        public bool TryDequeue(out ExportJob job) => _jobs.TryDequeue(out job);
    }
}
