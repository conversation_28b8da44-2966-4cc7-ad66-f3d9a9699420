/* Filter styling - extracted from pr-data.component.scss for reusability */
.filter-container {
  padding: 20px 24px 0;
  background-color: var(--card-bg-color, #ffffff);
  border-bottom: 1px solid var(--border-color, #e0e0e0);
  display: flex;
  gap: 16px;
  align-items: center;
  justify-content: space-between;
  
  /* Responsive behavior for mobile devices */  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
    padding: 16px 16px 8px;
    gap: 12px;
  }
}

.filter-container mat-form-field {
  width: 100%;
  max-width: 200px;
  
  @media (max-width: 768px) {
    max-width: none;
  }
}

/* Date range container styling */
.date-range-container {
  display: flex;
  gap: 16px;
  flex: 2;
  align-items: center;
    @media (max-width: 768px) {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 8px;
    width: 100%;
    
    mat-form-field {
      flex: 1 1 calc(50% - 8px);
      min-width: 120px;
    }
  }
    @media (max-width: 480px) {
    flex-direction: column;
    
    mat-form-field {
      flex: 1 1 100%;
    }
  }
  
  /* Optimize date pickers for touch */
  ::ng-deep .mat-datepicker-toggle {
    @media (max-width: 768px) {
      .mat-datepicker-toggle-default-icon {
        width: 24px;
        height: 24px;
      }
      
      .mat-mdc-button-touch-target {
        width: 48px;
        height: 48px;
      }
    }
  }
  
  /* Ensure date picker calendar is properly positioned on mobile */
  ::ng-deep .mat-datepicker-content {
    @media (max-width: 768px) {
      margin-top: 4px;
    }
  }
}

/* Clear date button styling */
.clear-date-button {
  height: 36px;
  margin-bottom: 10px;
  background-color: transparent;
  color: var(--primary-color, #0078d4);
  
  &:disabled {
    color: var(--disabled-color, #cccccc);
  }
  
  mat-icon {
    margin-right: 4px;
    font-size: 18px;
  }
  
  @media (max-width: 768px) {
    margin-bottom: 0;
    margin-top: 4px;
    align-self: flex-start;
  }
}

::ng-deep .filter-container .mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-text-field__input {
    margin-left: 10px !important;
}

/* Status filter styling */
.status-filter {
  max-width: 200px;
  flex: 1;
  
  @media (max-width: 768px) {
    max-width: none;
    margin-bottom: 8px;
  }
}

/* Search input styling */
.search-input {
  max-width: 250px;
  flex: 1;
  
  @media (max-width: 768px) {
    max-width: none;
    margin-top: 8px;
    order: 3; /* Move search input to the end on mobile */
  }
}

/* Improved search field appearance */
::ng-deep .filter-container .mat-mdc-form-field {
  .mat-mdc-form-field-icon-suffix {
    display: flex;
    align-items: center;
  }
  
  .mat-mdc-form-field-hint {
    color: var(--primary-color, #0078d4);
    font-style: italic;
  }
  
  /* Improve touch targets on mobile */
  @media (max-width: 768px) {
    .mat-mdc-form-field-infix {
      min-height: 48px;
    }
    
    .mat-mdc-text-field-wrapper {
      padding-bottom: 0;
    }
    
    .mat-mdc-form-field-subscript-wrapper {
      height: 20px;
    }
    
    .mat-mdc-form-field-icon-suffix {
      padding: 0 4px;
    }
    
    .mdc-text-field--outlined {
      padding-left: 8px;
      padding-right: 8px;
    }
  }
}

/* Active filters section styling */
.active-filters {
  padding: 8px 24px;
  background-color: var(--card-bg-color, #ffffff);
  border-bottom: 1px solid var(--border-color, #e0e0e0);
  
  @media (max-width: 768px) {
    padding: 8px 16px;
  }
}

.active-filters-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 10px;
  width: 100%;
  
  .filters-left {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
    flex: 1;
    min-width: 0; /* Prevent flex items from overflowing */
  }
  
  .filters-right {
    display: flex;
    justify-content: flex-end;
    margin-left: auto; /* Push to the right */
    
    .clear-all-btn {
      height: 32px;
      line-height: 32px;
      padding: 0 12px;
      font-size: 13px;
      white-space: nowrap;
      background-color: #f5f5f5;
      border: 1px solid #e0e0e0;
      border-radius: 10px;
      mat-icon {
        font-size: 16px;
        width: 16px;
        height: 16px;
        margin-right: 4px;
      }
      
      &:hover {
        background-color: rgba(244, 67, 54, 0.08);
      }
      
      @media (max-width: 480px) {
        margin-left: 0;
        align-self: flex-end;
      }
    }
  }
}

.active-filters-label {
  margin-right: 8px;
  font-weight: 500;
  color: var(--text-secondary-color, #616161);
}

.filter-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
    mat-chip {
    height: 28px;
    font-size: 13px;
    
    mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
      margin-left: 4px;
      cursor: pointer;

      &:hover {
        color: var(--error-color);
      }
    }
  }
}

/* Adjustments for small screens */
@media (max-width: 480px) {
  .filter-container {
    gap: 8px;
    padding: 12px 12px 0;
  }
  
  .date-range-container mat-form-field {
    margin-bottom: 0;
  }
  
  .date-range-container {
    padding-bottom: 8px;
    
    .clear-date-button {
      width: 100%;
      justify-content: center;
      margin-top: 8px;
    }
  }
  
  .filter-chips {
    flex-grow: 1;
  }
}

/* Responsive handling for the active filters container on mobile */
@media (max-width: 480px) {
  .active-filters-container {
    flex-direction: row;
    align-items: flex-start;
    
    .filters-left {
      flex: 1;
      margin-right: 8px;
    }
    
    .filters-right {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      flex-shrink: 0;
    }
  }
}
