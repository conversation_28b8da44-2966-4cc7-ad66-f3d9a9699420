﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using AutoMapper;
using MediatR;
using RIB.PRMetrics.Application.Dto;
using RIB.PRMetrics.Application.Interface.Persistence;
using RIB.PRMetrics.Application.UseCases.Commons.Bases;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetExcelReport
{
    /// <summary>
    /// Handles the <see cref="GetExcelReportQuery"/> to retrieve an exported Excel report by UUID.
    /// </summary>
    public class GetExcelReportHandler : IRequestHandler<GetExcelReportQuery, BaseReponseGeneric<ExportExcelReportDto>>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        /// <summary>
        /// Initializes a new instance of the <see cref="GetExcelReportHandler"/> class.
        /// </summary>
        /// <param name="unitOfWork">Unit of work for repository access.</param>
        /// <param name="mapper">AutoMapper instance for mapping entities to DTOs.</param>
        public GetExcelReportHandler(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        /// <summary>
        /// Handles the query to fetch the Excel export report by UUID.
        /// </summary>
        /// <param name="request">The query containing the UUID of the export report.</param>
        /// <param name="cancellationToken">Cancellation token.</param>
        /// <returns>A response containing the <see cref="ExportExcelReportDto"/> if found.</returns>
        public async Task<BaseReponseGeneric<ExportExcelReportDto>> Handle(GetExcelReportQuery request, CancellationToken cancellationToken)
        {
            var response = new BaseReponseGeneric<ExportExcelReportDto>();

            try
            {
                var excelExportReport = await _unitOfWork.GitPullRequestsRepository.GetExcelReportAsync(request.Uuid);

                if (excelExportReport is not null)
                {
                    response.Data = _mapper.Map<ExportExcelReportDto>(excelExportReport);
                    response.Succcess = true;
                    response.Message = "Excel Export File fetched successfully.";
                }
                else
                {
                    response.Succcess = false;
                    response.Message = "Excel Export File not found.";
                }
            }
            catch (Exception ex)
            {
                response.Succcess = false;
                response.Message = $"Error occurred: {ex.Message}";
            }

            return response;
        }
    }
}
