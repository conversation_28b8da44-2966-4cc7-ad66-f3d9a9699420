﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using MediatR;
using Microsoft.AspNetCore.Mvc;
using RIB.PRMetrics.Application.UseCases.Commons.Bases;
using System.ComponentModel.DataAnnotations;
using RIB.PRMetrics.Application.Dto;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Commands.TakeActionExcelReport
{
    /// <summary>
    /// Command to perform an action (Pause, Resume, Cancel) on an Excel report export process.
    /// </summary>
    public class TakeActionExcelReportCommand : IRequest<BaseReponseGeneric<ProcessProgressDto>>
    {
        /// <summary>
        /// Gets or sets the UUID of the export job.
        /// This value is required and is passed as a query parameter.
        /// </summary>
        [Required(ErrorMessage = "uuid is required.")]
        [FromQuery(Name = "uuid")]
        public string Uuid { get; set; }

        /// <summary>
        /// Gets or sets the job status to apply to the export job (e.g., Pause, Resume, Cancel).
        /// This value is required and is passed as a query parameter.
        /// </summary>
        [Required(ErrorMessage = "JobStatus is required.")]
        [FromQuery(Name = "jobStatus")]
        public string JobStatus { get; set; }
    }
}
