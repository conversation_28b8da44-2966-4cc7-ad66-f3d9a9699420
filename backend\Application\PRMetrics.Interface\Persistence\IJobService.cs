﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using RIB.PRMetrics.Domain.Entities;

namespace RIB.PRMetrics.Application.Interface.Persistence
{
    /// <summary>
    /// Defines the contract for a service that executes export jobs asynchronously.
    /// </summary>
    public interface IJobService
    {
        /// <summary>
        /// Executes the specified export job asynchronously.
        /// </summary>
        /// <param name="job">The <see cref="ExportJob"/> to be executed.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task ExecuteAsync(ExportJob job);
    }
}
