﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Application.Dto
{
    /// <summary>
    /// Represents the data required for exporting a report to an Excel file.
    /// Contains the file name, content as a byte array, and the MIME content type.
    /// </summary>
    public class ExportExcelReportDto
    {
        /// <summary>
        /// Gets or sets the name of the Excel file, including the file extension.
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// Gets or sets the binary content of the Excel file.
        /// </summary>
        public byte[] FileContent { get; set; }

        /// <summary>
        /// Gets or sets the MIME type of the file (e.g., "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet").
        /// </summary>
        public string ContentType { get; set; }
    }
}
