<!-- Filter container with date range picker, status filters and search -->
<div class="filter-container">
  <!-- Status filter moved to top for mobile views -->
  <mat-form-field appearance="outline" class="status-filter">
    <mat-label>Status</mat-label>
    <mat-select
      [(ngModel)]="statusFilter"
      (selectionChange)="onStatusFilterChange()"
    >
      <mat-option *ngFor="let option of statusOptions" [value]="option.value">{{
        option.label
      }}</mat-option>
    </mat-select>
  </mat-form-field>

  <!-- Date range picker -->
  <div class="date-range-container">
    <mat-form-field appearance="outline">
      <mat-label>Start Date</mat-label>
      <input
        matInput
        [matDatepicker]="startPicker"
        [(ngModel)]="startDate"
        (dateInput)="onDateRangeChange()"
        (dateChange)="onDateRangeChange()"
      />
      <mat-datepicker-toggle
        matSuffix
        [for]="startPicker"
      ></mat-datepicker-toggle>
      <mat-datepicker #startPicker></mat-datepicker>
    </mat-form-field>

    <mat-form-field appearance="outline">
      <mat-label>End Date</mat-label>
      <input
        matInput
        [matDatepicker]="endPicker"
        [(ngModel)]="endDate"
        (dateInput)="onDateRangeChange()"
        (dateChange)="onDateRangeChange()"
      />
      <mat-datepicker-toggle
        matSuffix
        [for]="endPicker"
      ></mat-datepicker-toggle>
      <mat-datepicker #endPicker></mat-datepicker>
    </mat-form-field>

    <!-- Clear date filter button -->
    <button
      mat-button
      class="clear-date-button"
      (click)="clearDateFilter()"
      matTooltip="Clear date filter"
      [hidden]="!startDate && !endDate"
    >
      <mat-icon>event_busy</mat-icon>
      Clear
    </button>
  </div>
  <!-- Search input on the right -->
  <mat-form-field appearance="outline" class="search-input">
    <mat-label>Pull Request ID</mat-label>
    <input
      matInput
      [(ngModel)]="_searchFilterValue"
      (ngModelChange)="onFilterChange($event)"
      [placeholder]="searchPlaceholder"
      #input
    />
    <button
      *ngIf="input.value"
      mat-icon-button
      matSuffix
      (click)="onSearchClear(input)"
      aria-label="Clear"
      matTooltip="Clear search"
    >
      <mat-icon>close</mat-icon>
    </button>
    <mat-icon *ngIf="!input.value" matSuffix>search</mat-icon>
  </mat-form-field>
</div>

<!-- Active filters display -->
<div class="active-filters" *ngIf="activeFilters$ | async as activeFilters">
  <div
    class="active-filters-container"
    *ngIf="
      activeFilters.hasSearch ||
      activeFilters.hasStatus ||
      activeFilters.hasDates
    "
  >
    <div class="filters-left">
      <span class="active-filters-label">Active Filters:</span>
      <div class="filter-chips">
        <mat-chip color="primary" selected *ngIf="activeFilters.hasSearch">
          PR ID: {{ activeFilters.searchDisplay }}
          <mat-icon matChipRemove (click)="onSearchClear(input)"
            >cancel</mat-icon
          >
        </mat-chip>

        <mat-chip color="primary" selected *ngIf="activeFilters.hasStatus">
          Status: {{ activeFilters.statusDisplay }}
          <mat-icon matChipRemove (click)="resetStatusFilter()"
            >cancel</mat-icon
          >
        </mat-chip>

        <mat-chip color="accent" selected *ngIf="activeFilters.hasDates">
          Date Range: {{ activeFilters.dateDisplay }}
          <mat-icon matChipRemove (click)="clearDateFilter()">cancel</mat-icon>
        </mat-chip>
      </div>
    </div>

    <div class="filters-right">
      <button
        mat-button
        color="warn"
        class="clear-all-btn"
        (click)="clearAllFilters()"
      >
        <mat-icon>filter_list_off</mat-icon>
        Clear All Filters
      </button>
    </div>
  </div>
</div>
