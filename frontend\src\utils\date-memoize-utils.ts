// Utility functions for date/time calculations with memoization for performance

import { state } from '@angular/animations';
import { calculateWorkingTime } from './date-utils';

// Memoization cache for parsed dates
const dateCache = new Map<string, Date>();

/**
 * Parse date string with memoization for performance
 * @param dateStr The date string to parse
 * @returns A Date object
 */
export function parseDateMemoized(dateStr: string): Date {
  if (!dateStr) {
    return new Date();
  }

  if (dateCache.has(dateStr)) {
    return dateCache.get(dateStr)!;
  }

  const date = new Date(dateStr);
  dateCache.set(dateStr, date);
  return date;
}

/**
 * Calculate duration between two dates in milliseconds
 * @param startDateStr Start date string
 * @param endDateStr End date string (defaults to current time)
 * @returns Duration in milliseconds
 */
export function calculateDurationMs(
  startDateStr: string,
  endDateStr?: string
): number {
  const startDate = parseDateMemoized(startDateStr);
  const endDate = endDateStr ? parseDateMemoized(endDateStr) : new Date();

  return Math.max(0, endDate.getTime() - startDate.getTime());
}

/**
 * Calculate duration between two dates in hours
 * @param startDateStr Start date string
 * @param endDateStr End date string (defaults to current time)
 * @returns Duration in hours as a number
 */
export function calculateDurationHours(
  startDateStr: string,
  endDateStr?: string
): number {
  return calculateDurationMs(startDateStr, endDateStr) / (1000 * 60 * 60);
}

/**
 * Format a duration in milliseconds to a human-readable string
 * @param durationMs Duration in milliseconds
 * @returns Human-readable duration string
 */
export function formatDuration(durationMs: number): string {
  if (durationMs <= 0) {
    return 'Less than a minute';
  }

  const hours = Math.floor(durationMs / (1000 * 60 * 60));
  const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));

  let result = '';
  if (hours > 0) {
    result += `${hours} ${hours === 1 ? 'hour' : 'hours'}`;
  }

  if (minutes > 0) {
    if (result) result += ' ';
    result += `${minutes} ${minutes === 1 ? 'minute' : 'minutes'}`;
  }

  return result || 'Less than a minute';
}

/**
 * Calculate and format the draft vs published time for a PR
 * Using calculateWorkingTime for accurate working hours calculation
 * @param stateEvents Array of state change events
 * @param creationDate PR creation date
 * @param currentState Current PR state
 * @returns Object with draft and published durations
 */
export function calculateDraftPublishedTime(
  stateEvents: any[],
  creationDateStr: string,
  currentState: string
): {
  totalDraftHours: any;
  totalPublishedHours: any;
  totalDraftFormatted: any;
  totalPublishedFormatted: any;
} {
  console.log(
    'Calculating draft vs published time for PR',
    stateEvents,
    creationDateStr,
    currentState
  );
  if (!stateEvents || !stateEvents.length) {
    // If no state events, assume the entire time was in the current state
    const workingTimeResult = calculateWorkingTime(
      creationDateStr,
      new Date().toISOString()
    );

    if (currentState === 'draft') {
      return {
        totalDraftHours: workingTimeResult.totalWorkingHours,
        totalPublishedHours: 0,
        totalDraftFormatted: workingTimeResult.totalWorkingHoursFormattedInWord,
        totalPublishedFormatted: 'None',
      };
    } else {
      return {
        totalDraftHours: 0,
        totalPublishedHours: workingTimeResult.totalWorkingHours,
        totalDraftFormatted: 'None',
        totalPublishedFormatted:
          workingTimeResult.totalWorkingHoursFormattedInWord,
      };
    }
  }

  let draftIntervals = [];
  let publishedIntervals = [];
  let lastEventDate = creationDateStr;
  let lastState = currentState === 'draft' ? 'published' : 'draft'; // Assume opposite of current to calculate first interval

  // Sort events by date
  const sortedEvents = [...stateEvents].sort(
    (a, b) =>
      parseDateMemoized(a.timestamp).getTime() -
      parseDateMemoized(b.timestamp).getTime()
  );

  // Process each state change to collect time intervals
  sortedEvents.forEach((event) => {
    const eventTimestamp = event.timestamp;

    if (lastState === 'draft') {
      draftIntervals.push({
        start: lastEventDate,
        end: eventTimestamp,
      });
    } else {
      publishedIntervals.push({
        start: lastEventDate,
        end: eventTimestamp,
      });
    }

    lastEventDate = eventTimestamp;
    lastState = event.state;
  });

  // Add final interval from last event to now
  const now = new Date().toISOString();
  if (lastState === 'draft') {
    draftIntervals.push({
      start: lastEventDate,
      end: now,
    });
  } else {
    publishedIntervals.push({
      start: lastEventDate,
      end: now,
    });
  }

  // Calculate total draft working time
  let totalDraftWorkingHours = 0;
  let totalDraftFormattedTime = 'None';

  if (draftIntervals.length > 0) {
    let totalDraftWorkingTime = draftIntervals.reduce((total, interval) => {
      const result = calculateWorkingTime(interval.start, interval.end);
      return total + result.totalWorkingHours;
    }, 0);

    totalDraftWorkingHours = totalDraftWorkingTime;

    // Format the draft time using calculateWorkingTime
    const draftHours = Math.floor(totalDraftWorkingTime);
    const draftMinutes = Math.round((totalDraftWorkingTime - draftHours) * 60);

    const parts = [];
    if (draftHours > 0) {
      parts.push(`${draftHours} ${draftHours === 1 ? 'hour' : 'hours'}`);
    }
    if (draftMinutes > 0 || parts.length === 0) {
      parts.push(
        `${draftMinutes} ${draftMinutes === 1 ? 'minute' : 'minutes'}`
      );
    }
    totalDraftFormattedTime = parts.join(', ');
  }

  // Calculate total published working time
  let totalPublishedWorkingHours = 0;
  let totalPublishedFormattedTime = 'None';

  if (publishedIntervals.length > 0) {
    let totalPublishedWorkingTime = publishedIntervals.reduce(
      (total, interval) => {
        const result = calculateWorkingTime(interval.start, interval.end);
        return total + result.totalWorkingHours;
      },
      0
    );

    totalPublishedWorkingHours = totalPublishedWorkingTime;

    // Format the published time using calculateWorkingTime
    const publishedHours = Math.floor(totalPublishedWorkingTime);
    const publishedMinutes = Math.round(
      (totalPublishedWorkingTime - publishedHours) * 60
    );

    const parts = [];
    if (publishedHours > 0) {
      parts.push(
        `${publishedHours} ${publishedHours === 1 ? 'hour' : 'hours'}`
      );
    }
    if (publishedMinutes > 0 || parts.length === 0) {
      parts.push(
        `${publishedMinutes} ${publishedMinutes === 1 ? 'minute' : 'minutes'}`
      );
    }
    totalPublishedFormattedTime = parts.join(', ');
  }

  return {
    totalDraftHours: totalDraftWorkingHours,
    totalPublishedHours: totalPublishedWorkingHours,
    totalDraftFormatted: totalDraftFormattedTime,
    totalPublishedFormatted: totalPublishedFormattedTime,
  };
}

/**
 * Calculate the durations for comment threads
 * Using calculateWorkingTime for accurate working hours calculation
 * @param thread The comment thread object
 * @param prCreationDateStr PR creation date string
 * @returns Object with calculated durations
 */
export function calculateCommentThreadDurations(
  thread: any,
  prCreationDateStr: string
): {
  activeDuration: number;
  activeDurationFormatted: string;
  fixedDuration?: number;
  fixedDurationFormatted?: string;
  hoursSincePRCreation: number;
  hoursSincePRCreationFormatted: string;
} {
  const publishedDate = thread.publishedDate;
  const lastUpdatedDate = thread.lastUpdatedDate;
  const status = thread.status?.toLowerCase();
  // Time from PR creation to thread creation using working time
  const prToThreadCreationResult = calculateWorkingTime(
    prCreationDateStr,
    publishedDate
  );
  const hoursSincePRCreation = prToThreadCreationResult.totalWorkingHours;
  const hoursSincePRCreationFormatted =
    prToThreadCreationResult.totalWorkingHoursFormattedInWord ||
    'Less than a minute';

  // For fixed threads, calculate how long it took to fix using working time
  if (status === 'fixed') {
    const fixedTimeResult = calculateWorkingTime(
      publishedDate,
      lastUpdatedDate
    );
    const fixedDuration = fixedTimeResult.totalWorkingHours;
    const fixedDurationFormatted =
      fixedTimeResult.totalWorkingHoursFormattedInWord || 'Less than a minute';

    return {
      activeDuration: 0, // Not active anymore
      activeDurationFormatted: 'None',
      fixedDuration,
      fixedDurationFormatted,
      hoursSincePRCreation,
      hoursSincePRCreationFormatted,
    };
  }

  // For active threads, calculate how long it has been active using working time
  const activeTimeResult = calculateWorkingTime(
    publishedDate,
    new Date().toISOString()
  );
  const activeDuration = activeTimeResult.totalWorkingHours;
  const activeDurationFormatted =
    activeTimeResult.totalWorkingHoursFormattedInWord || 'Less than a minute';

  return {
    activeDuration,
    activeDurationFormatted,
    hoursSincePRCreation,
    hoursSincePRCreationFormatted,
  };
}

/**
 * Enhance comments with calculated active duration and response time
 * Using calculateWorkingTime for accurate working hours calculation
 * @param comments The array of comments
 * @returns Comments with calculated durations
 */
export function enhanceCommentsWithDurations(comments: any[]): any[] {
  if (!comments || !comments.length) {
    return [];
  }

  const enhanced = [...comments];

  for (let i = 0; i < enhanced.length; i++) {
    const comment = enhanced[i];

    // For the first comment, there's no response time
    if (i === 0) {
      if (enhanced.length > 1) {
        // Calculate time to first response if there are further comments
        const nextComment = enhanced[1];
        const workingTimeResult = calculateWorkingTime(
          comment.publishedDate,
          nextComment.publishedDate
        );
        comment.responseTime = workingTimeResult.totalWorkingHours;
        comment.responseTimeFormatted =
          workingTimeResult.totalWorkingHoursFormattedInWord ||
          'Less than a minute';
      }
      continue;
    }

    // For subsequent comments, calculate response time from previous comment using working time
    const prevComment = enhanced[i - 1];
    const workingTimeResult = calculateWorkingTime(
      prevComment.publishedDate,
      comment.publishedDate
    );
    comment.responseTime = workingTimeResult.totalWorkingHours;
    comment.responseTimeFormatted =
      workingTimeResult.totalWorkingHoursFormattedInWord ||
      'Less than a minute';
    // For active comments (last in thread), calculate active duration using working time
    if (i === enhanced.length - 1) {
      const activeTimeResult = calculateWorkingTime(
        comment.publishedDate,
        new Date().toISOString()
      );
      comment.activeTime = activeTimeResult.totalWorkingHours;
      comment.activeTimeFormatted =
        activeTimeResult.totalWorkingHoursFormattedInWord ||
        'Less than a minute';
    }
  }

  return enhanced;
}
