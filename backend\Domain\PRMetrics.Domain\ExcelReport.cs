﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Domain
{
    /// <summary>
    /// Represents a detailed report of a pull request including metadata and comments.
    /// </summary>
    public class ExcelReport
    {
        /// <summary>
        /// Gets or sets the unique identifier for the pull request.
        /// </summary>
        public int PullRequestId { get; set; }

        /// <summary>
        /// Gets or sets the title of the pull request.
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// Gets or sets the status of the pull request (e.g., open, closed, merged).
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// Gets or sets the creation date of the pull request.
        /// </summary>
        public DateTime CreationDate { get; set; }

        /// <summary>
        /// Gets or sets the number of active comments on the pull request.
        /// </summary>
        public int ActiveComment { get; set; }

        /// <summary>
        /// Gets or sets the number of resolved comments on the pull request.
        /// </summary>
        public int ResolvedComment { get; set; }

        /// <summary>
        /// Gets or sets the total number of comments on the pull request.
        /// </summary>
        public int TotalComment { get; set; }

        /// <summary>
        /// Gets or sets the name of the project containing the pull request.
        /// </summary>
        public string Project { get; set; }

        /// <summary>
        /// Gets or sets the name of the repository containing the pull request.
        /// </summary>
        public string Repository { get; set; }

        /// <summary>
        /// Gets or sets the author of the pull request.
        /// </summary>
        public string Author { get; set; }

        /// <summary>
        /// Gets or sets the reviewer(s) of the pull request.
        /// </summary>
        public string Reviewer { get; set; }

        /// <summary>
        /// Gets or sets the source branch of the pull request.
        /// </summary>
        public string SourceBranch { get; set; }

        /// <summary>
        /// Gets or sets the status of the pipeline related to the pull request.
        /// </summary>
        public string PipelineStatus { get; set; }

        /// <summary>
        /// Gets or sets additional details about the pipeline run.
        /// </summary>
        public string PipelineDetails { get; set; }
    }
}
