import { Route } from '@angular/router';
import { PatLoginComponent } from '../components/pat-login/pat-login.component';
import { PrDataComponent } from '../components/pr-data/pr-data.component';
import { DashboardComponent } from '../components/dashboard/dashboard.component';
import { ThreadsComponent } from '../components/threads/threads.component';
import { PrDetailsComponent } from '../components/pr-details/pr-details.component';
import { AuthGuardService } from '../services/auth-guard.service';
import { NotFoundComponent } from '../components/not-found/not-found.component';

export const appRoutes: Route[] = [
        { path: '', component: PatLoginComponent },
        { 
          path: 'pull-requests', 
          component: PrDataComponent,
          canActivate: [AuthGuardService]
        },
        { 
          path: 'pr-details/:id', 
          component: PrDetailsComponent,
          canActivate: [AuthGuardService]
        },
        { 
          path: 'dashboard', 
          component: DashboardComponent,
          canActivate: [AuthGuardService]
        },
        { 
          path: 'threads', 
          component: ThreadsComponent,
          canActivate: [AuthGuardService]
        },
        { path: 'next', redirectTo: 'pull-requests', pathMatch: 'full' },
        { path: '404', component: NotFoundComponent },
        { path: '**', component: NotFoundComponent } // Catch-all route for 404
];
