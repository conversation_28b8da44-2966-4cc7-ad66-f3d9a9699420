﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using FluentValidation;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetExcelReport
{
    /// <summary>
    /// Validator for <see cref="GetExcelReportQuery"/> to ensure required fields are valid.
    /// </summary>
    public class GetExcelReportValidator : AbstractValidator<GetExcelReportQuery>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="GetExcelReportValidator"/> class,
        /// defining validation rules for the query.
        /// </summary>
        public GetExcelReportValidator()
        {
            RuleFor(x => x.Uuid)
                .NotEmpty().WithMessage("Uuid is required.")
                .NotNull().WithMessage("Uuid must not be null.");
        }
    }
}
